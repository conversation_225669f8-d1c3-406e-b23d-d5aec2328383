src:
  config:
    type: "dir"
    path: "config"
    copy_from_src: true
    exclude:
      placeholder: "placeholder*"
      entrypoint.sh: "*/airflow/entrypoint.sh"
  airflow_entrypoint_override:
    type: "file"
    path: "config/airflow/entrypoint.sh"
  core:
    type: "dir"
    path: "core"
    copy_from_src: true
    exclude:
      utils.py: "*/dags/utils.py"
      placeholder: "placeholder*"
  core_utils_override:
    type: "file"
    path: "core/dags/utils.py"
  exchange:
    type: "dir"
    path: "exchange"
    copy_from_src: true
    override: true
    exclude:
      placeholder: "placeholder*"
  .env:
    type: "env"
    path: ".env"
  .env.test:
    type: "env"
    path: ".env.test"
    dest: ".env"
    target: "test"
  .env.production:
    type: "env"
    path: ".env.production"
    dest: ".env"
    target: "production"
  docker-compose.yml:
    type: "yml"
    path: "docker-compose.yml"
  localtime:
    type: "file"
    path: "localtime"


data:
  kafka:
    type: "dir"
    path: "kafka"
    mode: 0o777
  minio:
    type: "dir"
    path: "minio"
    mode: 0o777
  mysql:
    type: "dir"
    path: "mysql"
    mode: 0o777
  postgres:
    type: "dir"
    path: "postgres"
  logs:
    type: "dir"
    path: "logs"
    subdirs:
      oms: "oms"
      cluster: "CLUSTER"
      slave: "SLAVE"
      watchdog: "WATCHDOG"
      exit_strat_log: "exit_strat_log"
      oms_db_executor: "oms_db_executor"
      exit_portal: "exit_portal"
      one_second_exit_application: "one_second_exit_application"
      redis_data_handler: "redis_data_handler"


pipeline:
  test_env:
    type: "env"
    path: "test_data/test_env"
  tests:
    type: "dir"
    path: "tests"
    copy_from_src: true
    override: true
    exclude:
      placeholder: "placeholder*"
  .ci:
    type: "dir"
    path: ".ci"
    copy_from_src: true
    append: true
    exclude:
      placeholder: "placeholder*"
  ruff.toml:
    type: "file"
    path: "ruff.toml"
  mypy.ini:
    type: "file"
    path: "mypy.ini"
