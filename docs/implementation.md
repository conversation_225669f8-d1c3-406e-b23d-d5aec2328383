
# Implementation of the Design Philosophy

To implement our design philosophy uniformly, we established a foundational repository, `docker_template`, which provides the essential directory structure, build scripts, and shared files such as pipeline file etc. necessary for maintaining consistency across all modules.

## `docker_template`: The Foundational Repository
   - **Purpose**: Provides a standard directory structure and key scripts that every derived module needs. This template minimizes redundant setup, enforces consistency, and enables straightforward deployment across projects.
   - **Project Structure**: The template includes:
     - **Core Directories**:
        - `core`: Contains reusable, extendable and non-overwritable functionality
        - `exchange`: Contains files that are open to overwrites, allowing exchange-specific functionality.
        - `config`: Stores configuration files necessary for running services, ensuring that environment-specific settings are centralized and easy to manage.
        - `tests`: Contains tests for functionality implemented in the `core` folder, enabling automated validation and reliability checks for reusable code.
        - `test_data`: Contains data files required to run the tests in `tests`, allowing consistent and reproducible testing conditions.
     - **Shared Files**: Includes build scripts, mypy config files, ruff config files etc. i.e. the files that are needed for every module.

```mermaid
graph TD
    T[docker_template / Derived Modules]
    
    subgraph Module Structure
        A[core]
        B[exchange]
        C[config]
        D[tests]
        E[test_data]
    end

    T --> A
    T --> B
    T --> C
    T --> D
    T --> E

```


## `docker_base`: The Root-Level Module
   - **Derived from the Template**: `docker_base` is forked from `docker_template` and serves as the root-level foundational module, containing no submodules itself.
   - **Extended by Other Modules**: It provides base functionality and structure upon which other modules can build. These modules add `docker_base` as a submodule, extending its functionality while maintaining structural consistency.

## Modules Extending `docker_base`
   - **Adding Submodules**: Modules derived from `docker_template` (e.g., `docker_ib`) incorporate `docker_base` as a submodule, creating a chain that follows our structured approach to submodule dependencies.
   - **Customizing Modules**: These modules can implement unique functionality by adding new code to `core` or `exchange`, overwriting existing files in `exchange`, updating configurations in `config`, or providing custom tests in `tests`. The build script allows easy file overwrites for files matching paths in submodules, supporting efficient customization.

```mermaid
graph TD
    A[docker_template]
    B[docker_base]
    C[docker_broker]
    D[docker_exchange]

    A -. forked .-> B
    A -. forked .-> C
    A -. forked .-> D
    B -- submodule --> C
    C -- submodule --> D
```

## Directives for Module Development and Updates
   - **Forking from the Template**: Each new module must be forked directly from `docker_template` to ensure structural consistency across projects. By adhering to this template, we maintain an organized, uniform structure that simplifies both development and maintenance.
   - **Shared Files Modifications**: Any updates to the build script, gitlab pipeline file or other shared files should be made first to `docker_template`. Once verified, these changes can be cherry-picked to other modules as needed, ensuring that all modules stay in sync with the latest updates without introducing inconsistencies.

This structured approach, with `docker_template` as the foundation, enables maintainability, clear organization, and consistent extensibility across the entire hierarchy.