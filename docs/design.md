# Design Philosophy
To ensure maintainability, reusability, and extensibility of orchestrating scripts needed for running trading for any exchange, we have adopted a structured approach. This philosophy emphasizes modular design, clear inheritance structures, and efficient use of submodules. The key design principles are outlined below:

## 1. Object-Oriented Programming (OOP) for Extensibility
We adhere to Object-Oriented Programming principles wherever possible, allowing for straightforward extension and customization. By structuring functionality in classes, we enable the use of inheritance, making it easy to override or extend behaviors as needed without extensive rewrites. This approach fosters code reusability and consistency across modules.

## 2. Use of Submodules for Modularity
To support seamless integration and customization across different exchanges or brokers, we include the relevant repository as a submodule in our target repository. For instance:
- docker_broker (specific to broker functionality, ex. docker_ib) extends docker_base, with docker_base acting as a submodule of docker_broker.
- Each module maintains a single submodule only, which allows for streamlined dependencies while accommodating a modular hierarchy. For example, an exchange module could contain a broker module as a submodule, which in turn could contain the docker_base as its submodule, creating a manageable chain of dependencies.

```mermaid
    graph LR
    B[docker_base]
    C[docker_broker]
    D[docker_exchange]

    B -. submodule .-> C
    C -. submodule .-> D
```

## 3. Simplified Imports with a Build Mechanism
In this project, we utilize a build mechanism instead of running directly from source files. This approach ensures that each module can seamlessly incorporate functionality from its submodule by:

- First copying relevant files from the submodule.
- Then adding or overwriting files from the current module as needed.

This layered build process consolidates imports and dependencies into a unified structure, streamlining the module's execution and removing the need for manual import path management.

## 4. Overwriting Files with Path-Based Overrides
The build script also supports complete file overwrites when needed, which simplifies custom functionality. Overwritten files must be at the same relative path in the current module as they are in the submodule.

For example, to override a file located at `exchange/apps/some_app.py` in a submodule, a file at `exchange/apps/some_app.py` in the current module will replace it. This ensures that exchange-specific variations, such as unique initialization arguments, can be implemented by simply rewriting specific files.

To minimize complexity, scripts that are subject to being overwritten should ideally be simple, focusing on class instantiation and function calls, without intricate logic.

```mermaid
flowchart TD
    
    subgraph Submodule Structure
        B[submodule files]
        C[File: exchange/apps/some_app.py]
    end
    
    subgraph Current Module Structure
        D[current module files]
        E[File: exchange/apps/some_app.py]
    end
    
    subgraph Overwriting Process
        F[Check relative path match]
        G[Overwrite submodule file with current module file]
    end

   
    B --> C
    D --> E
    F -->|Yes| G
    C --> F
    E --> F

```


## 5. Consistent Project Structure Across Modules
To standardize the structure across modules, each module always includes the following two directories:

- core: Contains reusable and extensible functionality. These files SHOULD NOT be overwritten
- exchange: Contains files that are subject to overwrites, allowing for customized functionality specific to each exchange.


This design philosophy provides a robust, scalable framework for building, customizing, and deploying orchestrating scripts across various exchanges or brokers, with minimal duplication and maximum code reuse.


