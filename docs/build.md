# Build Script Summary

This build script is a flexible and configurable tool designed for building project directories based on a specified configuration. Here’s an overview of its features:

## Requirements
- Python 3.x
- The following libraries (install via pip if needed):
  - `pyyaml`
  - `jinja2`

## Command-line Usage
Run the script with the following command:
```bash
python build_script.py --src <source_directory> --build <build_directory>
```
Arguments:
- `--src`: The project’s source directory.
- `--build`: The target build directory where the processed files will be saved.


## Core Features

1. **Directory Structure Creation**
   - Creates directories and subdirectories in the specified build location as per the configuration.
   - Supports setting file permissions on created directories.

2. **Copying Files with Conditions**
   - Copies files and directories from the source project to the build directory.
   - Allows conditional copying, such as excluding specific files or folders, and enables optional overriding of existing files.

3. **Environment File Merging**
   - Merges `.env` files from the source and build directories, with new values overriding existing ones in the build.
   - Parses and writes environment variables to handle configuration customization.

4. **Docker Compose File Merging**
   - Merges Docker Compose YAML files by combining dictionaries, enabling seamless integration of multiple configurations.

5. **Git Submodule Support**
   - Identifies Git submodules within the project and recursively builds them using their own configuration files if available.
   - Submodule build outputs are integrated into the main build structure.

6. **SQL Template Rendering**
   - Renders a SQL template using Jinja2 by substituting variables from the `.env` file.
   - Generates a final SQL file, which includes details like `database_name` and table names based on environment variables.

## Configuration: build.yml
The script relies on a build.yml file, specifying the following structure:

- `type`: Specifies the type of the item (supports dir, file, env, and docker-compose).
- `path`: The relative path within the source directory.
- `copy_from_src` (optional): A boolean to indicate if contents should be copied from the source.
- `subdirs` (optional): Dictionary defining subdirectory paths.
- `override` (optional): Boolean to replace existing content in the build directory.
- `append` (optional): Boolean to append contents if a file exists.
- `exclude` (optional): List of file paths to exclude.
- `mode` (optional): File permission mode, defaults to 0o775.


## Workflow
1. **Run Submodule Builds**: If submodules have build.yml files, the script builds them recursively.
2. **Load Configuration**: Loads the build.yml configuration for processing directories, files, .env files, and Docker Compose files.
3. **Process Entries**: For each entry:
   - If type is dir, directory_handler manages directory copying, permissions, etc.
   - If type is file, file_handler copies individual files.
   - If type is env, env_handler merges .env files.
   - If type is docker-compose, compose_handler merges Docker Compose files.
4. **Post Build**: post_build generates an SQL file by rendering a template with environment variables.

## Additional Features

- **Error Handling and Reporting**: Provides informative messages and error checks, like verifying the existence of source files and submodule configurations.
- **Deep Dictionary Merging**: Supports deep merging of dictionaries for handling nested configurations, particularly useful for Docker Compose and environment configurations.

---

Overall, this script serves as a customizable, extensible build tool that can streamline the setup of structured build environments, automate dependency management with submodules, and maintain consistency across environments.
