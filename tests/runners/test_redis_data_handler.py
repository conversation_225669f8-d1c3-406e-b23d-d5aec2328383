import pytest
from unittest.mock import Mock, patch, AsyncMock
import pandas as pd
import fakeredis.aioredis as fkaioredis

# Patch balte_initializer and balte_config before importing the module under test
with patch("balte.utility_inbuilt.balte_initializer"), patch(
    "balte.balte_config"
) as mock_balte_config_module, patch(
    "core.helpers.utils.get_local_timestamp"
) as mock_local_timestamp:
    mock_balte_config_module.symbol_to_balte_id = {
        "NIFTY": 5001,
        "BANKNIFTY": 5002,
        "CRUDEOIL": 7011,
    }
    mock_balte_config_module.MARKET_CLOSE_HOUR = 15
    mock_balte_config_module.MARKET_CLOSE_MINUTE = 30
    mock_balte_config_module.TIMEZONE = "Asia/Kolkata"

    mock_local_timestamp.return_value = pd.Timestamp("2023-01-01 15:30:00")

    from exchange.runners.redis_data_handler_base import RedisDataHandlerRunnerBase


@pytest.fixture(scope="class")
def mock_datasnap_client():
    """Fixture to provide a mocked DataSnap client."""
    mock_client = Mock()
    mock_client.get_latest_second = Mock()
    mock_client.get_tick_data = Mock()
    mock_client.close = Mock()
    return mock_client


@pytest.fixture(scope="class")
def redis_data_handler(mock_datasnap_client):
    """Fixture to provide a RedisDataHandlerRunnerBase instance with mocked dependencies."""
    with patch(
        "exchange.runners.redis_data_handler_base.DataSnapClient"
    ) as mock_datasnap_class, patch(
        "balte.utility_inbuilt.balte_initializer"
    ) as mock_initializer, patch(
        "exchange.runners.redis_data_handler_base.RedisDataHandlerRunnerBase.setup_logger"
    ) as mock_setup_logger, patch(
        "exchange.runners.redis_data_handler_base.asyncio.Future"
    ) as mock_future:
        mock_datasnap_class.return_value = mock_datasnap_client
        mock_initializer.return_value = None
        mock_setup_logger.return_value = Mock()

        # Create a mock Future that doesn't require an event loop
        future_instance = Mock()
        future_instance.done.return_value = False
        future_instance.set_result = Mock()
        mock_future.return_value = future_instance

        handler = RedisDataHandlerRunnerBase()
        return handler


class TestRedisDataHandlerRunnerBaseInitialization:
    """Test cases for RedisDataHandlerRunnerBase initialization."""

    def test_init_sets_redis_connection_params(self, redis_data_handler):
        """Test that initialization sets Redis connection parameters."""
        assert redis_data_handler.redis_host == "REDIS_HOST"
        assert redis_data_handler.redis_port == 1
        assert redis_data_handler.redis_db == ""
        assert redis_data_handler.redis_client is None

    def test_init_creates_datasnap_client(
        self, redis_data_handler, mock_datasnap_client
    ):
        """Test that initialization creates a DataSnap client."""
        assert redis_data_handler.datasnap_client == mock_datasnap_client

    def test_init_sets_market_closing_time(self, redis_data_handler):
        """Test that initialization sets market closing time correctly."""
        expected_time = (
            pd.Timestamp("2023-01-01 15:30:00").normalize().replace(hour=15, minute=30)
        )
        assert redis_data_handler.market_closing_time.hour == expected_time.hour
        assert redis_data_handler.market_closing_time.minute == expected_time.minute


class TestRedisDataHandlerRunnerBaseCaching:
    """Test cases for caching functionality."""

    def test_get_balte_id_caches_result(self, redis_data_handler):
        """Test that get_balte_id caches the result."""
        contract = "CRUDEOIL17-Jul-2025CE595000"

        result1 = redis_data_handler.get_balte_id(contract)
        assert result1 == 595002025071717011
        assert (
            redis_data_handler.contract_to_balte_id_cache[contract]
            == 595002025071717011
        )

    def test_clear_balte_id_cache(self, redis_data_handler):
        """Test clearing the balte_id cache."""
        redis_data_handler.contract_to_balte_id_cache["TEST"] = 123
        redis_data_handler.clear_balte_id_cache()
        assert redis_data_handler.contract_to_balte_id_cache == {}


class TestRedisDataHandlerRunnerBaseRedisOperations:
    """Test cases for Redis operations."""

    @pytest.mark.asyncio
    async def test_connect_to_redis_creates_client(self, redis_data_handler):
        """Test that connect_to_redis creates a Redis client."""
        fake_redis = fkaioredis.FakeRedis(decode_responses=True)

        with patch(
            "exchange.runners.redis_data_handler_base.aioredis.from_url"
        ) as mock_from_url:

            async def async_from_url(*args, **kwargs):
                return fake_redis

            mock_from_url.side_effect = async_from_url

            await redis_data_handler.connect_to_redis()

            mock_from_url.assert_called_once()
            assert redis_data_handler.redis_client is not None
            assert redis_data_handler.redis_client == fake_redis

    @pytest.mark.asyncio
    async def test_connect_to_redis_skips_if_already_connected(
        self, redis_data_handler
    ):
        """Test that connect_to_redis skips if already connected."""
        mock_client = AsyncMock()
        redis_data_handler.redis_client = mock_client

        with patch("redis.asyncio.from_url") as mock_from_url:
            await redis_data_handler.connect_to_redis()
            mock_from_url.assert_not_called()

    @pytest.mark.asyncio
    async def test_store_balte_id_to_ltp_in_redis_success(self, redis_data_handler):
        """Test successful storage of balte_id to LTP mappings."""
        fake_redis = fkaioredis.FakeRedis(decode_responses=True)
        redis_data_handler.redis_client = fake_redis

        tick_data = ["1|NIFTY|field2|100.50|field4", "2|BANKNIFTY|field2|200.75|field4"]

        await redis_data_handler.store_balte_id_to_ltp_in_redis(tick_data)

        nifty_balte_id = redis_data_handler.get_balte_id("NIFTY")
        banknifty_balte_id = redis_data_handler.get_balte_id("BANKNIFTY")

        nifty_ltp = await fake_redis.get(str(nifty_balte_id))
        banknifty_ltp = await fake_redis.get(str(banknifty_balte_id))

        assert nifty_ltp == "100.50"
        assert banknifty_ltp == "200.75"

    @pytest.mark.asyncio
    async def test_store_balte_id_to_ltp_raises_if_no_client(self, redis_data_handler):
        """Test that store_balte_id_to_ltp raises exception if Redis client not initialized."""
        redis_data_handler.redis_client = None
        tick_data = ["1|NIFTY|field2|100.50|field4"]

        with pytest.raises(Exception, match="Redis client is not initialized"):
            await redis_data_handler.store_balte_id_to_ltp_in_redis(tick_data)


class TestRedisDataHandlerRunnerBaseMarketOperations:
    """Test cases for market operations."""

    @pytest.mark.asyncio
    async def test_market_termination_checker_sets_flag(self, redis_data_handler):
        """Test that market_termination_checker sets the market closed flag."""
        # Set market closing time to be in the past
        redis_data_handler.market_closing_time = pd.Timestamp("2023-01-01 15:29:59")

        await redis_data_handler.market_termination_checker()

        redis_data_handler.is_market_closed.set_result.assert_called_once_with(True)

        # Reset the mock call
        redis_data_handler.is_market_closed.set_result.reset_mock()

    def test_run_method(self, redis_data_handler):
        """Test the synchronous run method."""
        with patch.object(
            redis_data_handler, "fetch_and_store_data"
        ) as mock_fetch, patch.object(
            redis_data_handler, "market_termination_checker"
        ) as mock_termination:
            mock_fetch.return_value = None
            mock_termination.return_value = None

            redis_data_handler.run()

            mock_fetch.assert_called_once()
            mock_termination.assert_called_once()

            # Reset the mock calls
            mock_fetch.reset_mock()
            mock_termination.reset_mock()

    @pytest.mark.asyncio
    async def test_market_termination_checker_with_future_time(
        self, redis_data_handler
    ):
        """Test market_termination_checker when market closing time is in the future."""
        redis_data_handler.market_closing_time = pd.Timestamp("2023-01-01 15:30:05")

        with patch("asyncio.sleep") as mock_sleep:
            await redis_data_handler.market_termination_checker()

            mock_sleep.assert_called_once_with(5.0)
            redis_data_handler.is_market_closed.set_result.assert_called_once_with(True)

            # Reset the mock call
            redis_data_handler.is_market_closed.set_result.reset_mock()
