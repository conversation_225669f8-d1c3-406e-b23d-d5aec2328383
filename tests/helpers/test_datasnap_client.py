import pytest
import grpc
import logging
from unittest.mock import Mock, patch
from exchange.helpers.redis_data_handler_components.datasnap_client import (
    DataSnapClient,
)
from toti.rpc import datasnapservice_pb2 as datasnap_service_pb2


@pytest.fixture(scope="class")
def mock_grpc_channel():
    """Fixture to provide a mocked gRPC channel."""
    with patch("grpc.insecure_channel") as mock_channel:
        mock_channel_instance = Mock()
        mock_channel.return_value = mock_channel_instance
        yield mock_channel_instance


@pytest.fixture(scope="class")
def mock_datasnap_stub():
    """Fixture to provide a mocked DataSnap stub."""
    mock_stub = Mock()
    mock_stub.get_tick_data = Mock()
    mock_stub.get_latest_second = Mock()
    return mock_stub


@pytest.fixture(scope="class")
def mock_logger():
    """Fixture to provide a mocked logger."""
    return Mock(spec=logging.Logger)


@pytest.fixture(scope="class")
def datasnap_client(mock_datasnap_stub, mock_logger):
    """Fixture to provide a DataSnapClient instance with mocked dependencies."""
    with patch(
        "exchange.helpers.redis_data_handler_components.rpc.datasnap_service.datasnap_service_pb2_grpc.DataSnapStub"
    ) as mock_stub_class:
        mock_stub_class.return_value = mock_datasnap_stub
        client = DataSnapClient(logger=mock_logger)
        client.stub = mock_datasnap_stub
        client.logger = mock_logger
        return client


class TestDataSnapClientInitialization:
    """Test cases for DataSnapClient initialization."""

    def test_init_creates_grpc_channel(self, mock_logger):
        """Test that initialization creates a gRPC channel with correct parameters."""
        with patch("grpc.insecure_channel") as mock_channel, patch(
            "exchange.helpers.redis_data_handler_components.rpc.datasnap_service.datasnap_service_pb2_grpc.DataSnapStub"
        ):
            DataSnapClient(logger=mock_logger)
            mock_channel.assert_called_once_with("DATASNAP_HOST:1")

    def test_init_creates_datasnap_stub(self, mock_grpc_channel, mock_logger):
        """Test that initialization creates a DataSnap stub."""
        with patch(
            "exchange.helpers.redis_data_handler_components.rpc.datasnap_service.datasnap_service_pb2_grpc.DataSnapStub"
        ) as mock_stub_class:
            client = DataSnapClient(logger=mock_logger)
            mock_stub_class.assert_called_once_with(mock_grpc_channel)
            assert client.stub == mock_stub_class.return_value

    def test_init_sets_channel_attribute(self, mock_grpc_channel, mock_logger):
        """Test that initialization sets the channel attribute."""
        with patch(
            "exchange.helpers.redis_data_handler_components.rpc.datasnap_service.datasnap_service_pb2_grpc.DataSnapStub"
        ):
            client = DataSnapClient(logger=mock_logger)
            assert client.channel == mock_grpc_channel


class TestDataSnapClientGetTickData:
    """Test cases for the get_tick_data method."""

    def test_get_tick_data_success(self, datasnap_client, mock_datasnap_stub):
        """Test successful get_tick_data call."""
        expected_response = datasnap_service_pb2.RepeatedArrayReply()
        expected_response.message.extend(["tick1", "tick2", "tick3"])
        mock_datasnap_stub.get_tick_data.return_value = expected_response

        result = datasnap_client.get_tick_data(
            "segment1", "2023-01-01T10:00:00", "utf-8"
        )

        assert result == expected_response

        # Reset the mock call
        mock_datasnap_stub.reset_mock()

    def test_get_tick_data_retry_on_error(self, datasnap_client, mock_datasnap_stub):
        """Test get_tick_data retries on gRPC errors and eventually succeeds."""
        expected_response = datasnap_service_pb2.RepeatedArrayReply()
        expected_response.message.extend(["tick1", "tick2"])

        grpc_error = grpc.RpcError()

        mock_datasnap_stub.get_tick_data.side_effect = [
            grpc_error,
            expected_response,
        ]

        # Mock time.sleep to avoid actual delays in tests
        with patch("time.sleep"), patch(
            "exchange.helpers.redis_data_handler_components.datasnap_client.send_message_to_gspace"
        ) as mock_send_message:
            result = datasnap_client.get_tick_data("segment1", "2023-01-01T10:00:00")

        assert result == expected_response
        assert mock_datasnap_stub.get_tick_data.call_count == 2
        mock_send_message.assert_called_once()

        # Reset the mock call
        mock_datasnap_stub.reset_mock()


class TestDataSnapClientGetLatestSecond:
    """Test cases for the get_latest_second method."""

    def test_get_latest_second_success(self, datasnap_client, mock_datasnap_stub):
        """Test successful get_latest_second call."""
        expected_response = datasnap_service_pb2.TimestampResponse()
        expected_response.timestamp = "2023-01-01T10:00:00"
        mock_datasnap_stub.get_latest_second.return_value = expected_response

        result = datasnap_client.get_latest_second("segment1")

        assert result == expected_response
        assert result.timestamp == "2023-01-01T10:00:00"

        # Reset the mock call
        mock_datasnap_stub.reset_mock()

    def test_get_latest_second_retry_on_error(
        self, datasnap_client, mock_datasnap_stub
    ):
        """Test get_latest_second retries on gRPC errors and eventually succeeds."""
        expected_response = datasnap_service_pb2.TimestampResponse()
        expected_response.timestamp = "2023-01-01T10:00:00"

        grpc_error = grpc.RpcError()

        mock_datasnap_stub.get_latest_second.side_effect = [
            grpc_error,
            expected_response,
        ]

        # Mock time.sleep to avoid actual delays in tests
        with patch("time.sleep"), patch(
            "exchange.helpers.redis_data_handler_components.datasnap_client.send_message_to_gspace"
        ) as mock_send_message:
            result = datasnap_client.get_latest_second("segment1")

        assert result == expected_response
        assert mock_datasnap_stub.get_latest_second.call_count == 2
        mock_send_message.assert_called_once()

        # Reset the mock call
        mock_datasnap_stub.reset_mock()


class TestDataSnapClientClose:
    """Test cases for the close method."""

    def test_close_calls_channel_close(self, datasnap_client):
        """Test that close method calls channel.close()."""
        datasnap_client.channel.close = Mock()

        datasnap_client.close()

        datasnap_client.channel.close.assert_called_once()
