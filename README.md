# Index
1. [Creating a new module](#creating-a-new-module)
2. [Build Process](#build-process)
3. [Git Management](#git-management)
4. [Deployment](#deployment-process)

---

# Creating a New Module

This section provides step-by-step instructions for creating a new module in the project by forking from the `docker_template` repository. By following these steps, you’ll ensure your module is aligned with the project’s structure and can easily integrate updates from the template repository.

## Steps to Create a New Module

### 1. Fork the `docker_template` Repository

Begin by forking the [docker_template](http://*************:15000/tradingorchestrator/docker_template) repository. This will serve as the foundational structure for your new module.

### 2. Add the Required Submodule

Each new module typically builds on functionality from a core module, like docker_base or docker_broker. You’ll need to add the relevant submodule to your newly created module repository.

For example, to add docker_base as a submodule, run the following command:

```bash
git submodule add -b dev git@*************:tradingorchestrator/docker_base.git docker_base
```

---

# Build Process

We use artifacts for deployment and hence we need to build the module. Artifacts are built by pipeline and usually there is no need to build locally, but in case of testing changes to the build script itself, refer below for how to run the build script.

## Running the Build Script Locally

To execute the build script, use the following command:

```bash
python build.py --src <project_directory> --build <build_directory>
```

### CLI Arguments
- `--src` (required): Specifies the source project directory path that contains the files to be built.
- `--build` (required): Defines the destination directory where the build will be outputted. If the directory already exists, it will be deleted before the new build process.

---

# Git Management

## Cloning
When a module is cloned, the submodules need to initialized explicitly using this command

### Cloning the repository

```bash
git clone <repo_url>
```

### Initializing and updating submodules

```bash
git submodule update --init --recursive
```

## Updating Submodules
To update a submodule to the latest commit in its repository:

1. Navigate to the submodule directory:

```bash
cd <path_to_submodule>
```

2. Pull the latest changes:

```bash
git pull origin <branch_name>
```

3. Return to the main repository and commit the submodule update:
```bash
cd ..
git add <path_to_submodule>
git commit -m "Update submodule <name>"
```

### Checking Out Different Branches with Submodules
If your project has branches that reference different commit IDs for a submodule, you need to explicitly update the submodule after checking out such branches. Here’s how to do it:

1. Check out the desired branch:
```bash
git checkout <branch_name>
```

2. Update the submodule to match the commit ID recorded in the branch:
```bash
git submodule update --recursive
```

## Syncing `docker_template` changes

Since all the repos are forked from docker_template, any changes made to `.gitlab-ci.yml` or `build.py` need to merged in `docker_template` and the same can be pulled into the repos by running the following commands

### Adding `docker_template` as an Upstream Remote

```bash
git remote add upstream git@*************:tradingorchestrator/docker_template.git
```

### Checking out files from upstream
```bash
git fetch upstream

git checkout upstream/dev -- build.py .gitlab-ci.yml
```

## Git Branch Naming Guidelines

For effective branch organization and streamlined deployments, adhere to the following conventions based on module type:

### Exchange-Level Modules
For exchange-level modules (e.g., `docker_krx`, `docker_us`, etc.), maintain the following branches:

- **dev**: Primary branch for ongoing development.
- **stage_branch**: Dedicated branch for test system deployments.
- **production**: Branch for production system deployments.

### Non-Exchange-Level Modules
For non-exchange-level modules (e.g., `docker_ib`, `docker_base`), only the **dev** branch is required. Projects that import these modules as submodules can manage versions by checking out different commits as needed.

---

# Deployment Process

Deployment relies on artifact generation through the GitLab CI pipeline, specifically when triggered from the **stage_branch** or **production_branch**. The resulting artifact contains the following two folders:

- **src**: Contains the application source code.
- **data**: Stores the logs and data generated by the services.

Artifacts can be downloaded to a specfic comp using the following command, where you need the replace the relevant parameters with the correct values

```bash
wget --header "PRIVATE-TOKEN: <personal access token>" -O artifacts.zip  http://*************:15000/api/v4/projects/<project id>/jobs/<job id>/artifacts ; unzip -q artifacts.zip
```

### 1. Where to get the personal access token from ?
Refer the steps mentioned [here](https://docs.gitlab.com/ee/user/profile/personal_access_tokens.html)

### 2. Where can I copy the project ID from ?
1. On the left sidebar, select Search or go to and find your project.
2. On the project overview page, in the upper-right corner, select `Actions` (three dots icon).
3. Select Copy project ID.

### 3. Where can I copy the job ID from ?
1. On the left sidebar, select Search or go to and find your project.
2. On the project overview page, in the left side panel, select Jobs menu (you can find this menu by hovering over the Build Menu)
3. On the jobs page find the latest `Publish` job for `stage_branch` in case of Test Deployment or `production` in case of Production Deployment. In the Job column on the page you would see an entry like this `#42343: Publish`, here `42343` is the job id.

## Initial Deployment
For the first-time deployment, copy both the **src** and **data** folders to the target environment.

- **NOTE:** While copying the folders prefer using the `cp` command with the following flags `cp -rPp`

## Updating an Existing Deployment
To update an already running deployment, follow these steps:

1. Run the `docker compose down` command in the existing **src** folder to stop the running services.
2. Replace only the **src** folder with the updated **src** from the artifact.
    - **NOTE:** Wile copying the src folder prefer using the `cp` command with the following flags `cp -rPp`
3. Run `docker compose up` command.

This process ensures minimal downtime and maintains a clean deployment structure.

## Setting up pytest on local server
To setup and run pytest on local server, follow the steps given below - 

```bash
cd pytest_setup
docker compose up -d --build
docker exec -it test_runner bash

# To run using env_balte_0.0.3 environment
conda activate env_balte_0.0.3

# To run using env_balte_1.0.0 environment
conda activate env_balte_1.0.0

pytest pipeline/tests/.
```

### How to write test cases once local testing environment is set up
* We should add new test case files in `tests` folder, and test data in `test_data` folder.
* The test cases are running after the `build` command inside the container. So, we should copy any file changes outside to container to build files inside the container from them to be reflected.
* All the files present in `core` and `exchange` folder are present in `/opt/build/src` folder inside the container. Use the following command to copy files from here - 

```bash
docker cp <path_to_file_outside_the_container> test_runner:/opt/build/src/<path_to_file_outside_the_container>
```
**For example:** To copy `core/runners/one_second_exit_application_base.py` inside the container, use the following command -

```bash
docker cp core/runners/one_second_exit_application_base.py test_runner:/opt/build/src/core/runners/one_second_exit_application_base.py
```
* All files inside `tests` and `test_data` folder are present in `/opt/build/pipeline` folder inside the container. Use the following command to copy files from here -

```bash
docker cp <path_to_file_outside_the_container> test_runner:/opt/build/pipeline/<path_to_file_outside_the_container>
```

**For example:** To copy `tests/helpers/test_position_manager.py` inside the container, use the following command -

```bash
docker cp tests/helpers/test_position_manager.py test_runner:/opt/build/pipeline/tests/helpers/test_position_manager.py  
```