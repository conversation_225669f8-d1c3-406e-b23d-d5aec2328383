FROM *************:15050/devops/commonlibs/balte/dev:latest

WORKDIR /app/docker_infra

COPY . ./

ARG BUILD_CONDA_ENV="env_balte_1.0.0"
ARG CONDA_ENV_NAME_OLD="env_balte_0.0.3"
ARG CONDA_ENV_NAME_NEW="env_balte_1.0.0"

RUN source activate ${BUILD_CONDA_ENV} && \
    pip install xxhash==3.5.0 && \
    python build.py --src . --build /opt/build && \
    cd /opt/build && \
    source /opt/conda/bin/activate ${CONDA_ENV_NAME_OLD} && \
    FILE=./pipeline/.ci/requirements/Testing/common.txt; if [ -f $FILE ]; then pip install -r $FILE; fi && \
    FILE=./pipeline/.ci/requirements/Testing/${CONDA_ENV_NAME_OLD}.txt; if [ -f $FILE ]; then pip install -r $FILE; fi && \
    FILE=./pipeline/.ci/scripts/Testing/common.sh; if [ -f $FILE ]; then source $FILE; fi && \
    FILE=./pipeline/.ci/scripts/Testing/${CONDA_ENV_NAME_OLD}.sh; if [ -f $FILE ]; then source $FILE; fi && \
    source /opt/conda/bin/activate ${CONDA_ENV_NAME_NEW} && \
    FILE=./pipeline/.ci/requirements/Testing/common.txt; if [ -f $FILE ]; then pip install -r $FILE; fi && \
    FILE=./pipeline/.ci/requirements/Testing/${CONDA_ENV_NAME_NEW}.txt; if [ -f $FILE ]; then pip install -r $FILE; fi && \
    FILE=./pipeline/.ci/scripts/Testing/${CONDA_ENV_NAME_NEW}.sh; if [ -f $FILE ]; then source $FILE; fi && \
    echo 'cd /opt/build && \
    export $(cat pipeline/test_data/test_env)' >> /root/.bashrc

CMD ["/bin/bash/"]
