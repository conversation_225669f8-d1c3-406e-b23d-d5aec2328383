[mypy]
exclude='test_data/'
warn_unused_configs = True


; Ensure full coverage
disallow_untyped_calls = True
disallow_untyped_defs = True
disallow_incomplete_defs = True
disallow_untyped_decorators = True
check_untyped_defs = True

; Restrict dynamic typing
disallow_any_generics = True
disallow_subclassing_any = False
warn_return_any = True

; Know exactly what you're doing
warn_redundant_casts = True
warn_unused_ignores = False
warn_unreachable = True
show_error_codes = True

; Explicit is better than implicit
no_implicit_optional = True


[mypy-balte.*,toti.*,slippage_analyzer.*,analytics_api,grpc.*]
ignore_missing_imports = True
