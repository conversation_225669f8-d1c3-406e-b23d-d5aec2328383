src:
  config:
    type: "dir"
    path: "config"
    copy_from_src: true
    exclude:
      crontab: "cron.txt"
      placeholder: "placeholder*"
  crontab:
    type: "crontxt"
    path: "config/cron.txt"
  core:
    type: "dir"
    path: "core"
    copy_from_src: true
    exclude:
      placeholder: "placeholder*"
  exchange:
    type: "dir"
    path: "exchange"
    copy_from_src: true
    override: true
    exclude:
      placeholder: "placeholder*"
  .env:
    type: "env"
    path: ".env"
  docker-compose.yml:
    type: "yml"
    path: "docker-compose.yml"
  localtime:
    type: "file"
    path: "localtime"


data:
  kafka:
    type: "dir"
    path: "kafka"
    mode: 0o777
  minio:
    type: "dir"
    path: "minio"
    mode: 0o777
  mysql:
    type: "dir"
    path: "mysql"
    mode: 0o777
  postgresql:
    type: "dir"
    path: "postgresql"
  alloy:
    type: "dir"
    path: "alloy"
  logs:
    type: "dir"
    path: "logs"
    subdirs:
      oms: "oms"
      cluster: "CLUSTER"
      slave: "SLAVE"
      watchdog: "WATCHDOG"
      exit_strat_log: "exit_strat_log"
      oms_db_executor: "oms_db_executor"
      exit_portal: "exit_portal"
      airflow: "airflow"
      one_second_exit_application: "one_second_exit_application"
  

pipeline:
  test_data:
    type: "dir"
    path: "test_data"
    copy_from_src: true
    exclude:
      logs: "airflow_test_config/logs/**/*"
  tests:
    type: "dir"
    path: "tests"
    copy_from_src: true
    exclude:
      placeholder: "placeholder*"
  .ci:
    type: "dir"
    path: ".ci"
    copy_from_src: true
    append: true
    exclude:
      placeholder: "placeholder*"
  ruff.toml:
    type: "file"
    path: "ruff.toml"
  mypy.ini:
    type: "file"
    path: "mypy.ini"
