from core.runners.live_trade_pickle_db_base import LiveTradePickleDbRunnerBase


class LiveTradePickleDbRunner(LiveTradePickleDbRunnerBase):
    """
    Each exchange can override this file to add exchange specific behaviour
    to each runner
    """

    pass


if __name__ == "__main__":
    live_trade_pickle_db_runner = LiveTradePickleDbRunner(
        allowed_segments=[
            "OPTIDX",
            "OPTSTK",
            "OPTCUR",
            "OPTCOM",
            "FUTSTK",
            "FUTIDX",
            "FUTCOM",
        ],
        allowed_universes=[
            "opt",
            "opt_onemin",
            "optcur",
            "optcom",
            "optstk",
            "optstk_onemin",
        ],
    )
    live_trade_pickle_db_runner.run()
