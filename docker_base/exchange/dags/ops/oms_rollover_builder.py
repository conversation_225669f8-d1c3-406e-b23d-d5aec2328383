from core.dags.ops.oms_ops_builder_base import (
    OMSRolloverDagBuilderBase,
)
from airflow.models import DAG


class OMSRolloverDagBuilder(OMSRolloverDagBuilderBase):
    """
    Each exchange can override this file to add exchange specific behaviour
    to each dag
    """

    pass


oms_rollover_dag_builder = OMSRolloverDagBuilder(
    dag_id="OMS_ROLLOVER", schedule_interval="00 09 * * 1-5"
)
dag = oms_rollover_dag_builder.build_dag()
