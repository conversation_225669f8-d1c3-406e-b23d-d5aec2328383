from core.dags.ops.dead_strat_exit_builder_base import (
    DeadStratExitDagBuilderBase,
)
from airflow.models import DAG


class DeadStratExitDagBuilder(DeadStratExitDagBuilderBase):
    """
    Each exchange can override this file to add exchange specific behaviour
    to each dag
    """

    pass


dead_strat_exit_dag_builder = DeadStratExitDagBuilder(
    dag_id="removed_strat_exit", schedule_interval="0 9 * * 1-5"
)
dag = dead_strat_exit_dag_builder.build_dag()
