from core.dags.monitoring.trading_checks_builder_base import (
    TradingCheckDagBuilderBase,
)
from airflow.models import DAG
import datetime
from core.helpers.configstore import SEGMENT
from core.helpers.utils import get_live_strats


class TradingCheckDagBuilder(TradingCheckDagBuilderBase):
    """
    Each exchange can override this file to add exchange specific behaviour
    to each dag
    """

    pass


trading_check_dag_builder = TradingCheckDagBuilder(
    dag_id="prod_trading_checks",
    schedule_interval="30 09 * * 1-5",
    trading_check_end_time=datetime.time(15, 20),
    no_trade_check_segments=SEGMENT.split(","),
    no_trade_threshold_min=15,
    pickle_state_check_strats=get_live_strats(),
    pickle_state_check_threshold_sec=500,
)
dag = trading_check_dag_builder.build_dag()
