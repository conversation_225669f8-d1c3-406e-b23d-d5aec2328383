from core.dags.monitoring.strat_wise_time_delta_builder_base import (
    StratWiseTimeDeltaBuilderBase,
)
from airflow.models import DAG


class StratWiseTimeDeltaBuilder(StratWiseTimeDeltaBuilderBase):
    """
    Each exchange can override this file to add exchange specific behaviour
    to each dag
    """

    pass


strat_wise_time_delta_dag_builder = StratWiseTimeDeltaBuilder(
    dag_id="create_strat_wise_time_delta", schedule_interval="40 15 * * *"
)
dag = strat_wise_time_delta_dag_builder.build_dag()
