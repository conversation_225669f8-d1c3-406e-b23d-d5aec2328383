from core.dags.monitoring.trading_pickle_db_check_builder_base import (
    TradePickleDbCheckBuilderBase,
)
from airflow.models import DAG
from core.helpers.configstore import EXCHANGE_TYPE, EXECUTION_LEVEL


class TradePickleDbCheckBuilder(TradePickleDbCheckBuilderBase):
    """
    Each exchange can override this file to add exchange specific behaviour
    to each dag
    """

    pass


trading_pickle_db_check_dag_builder = TradePickleDbCheckBuilder(
    dag_id=f"pickle_db_trade_check_{EXCHANGE_TYPE}_{EXECUTION_LEVEL}",
    schedule_interval="30 17 * * 1-5",
)
dag = trading_pickle_db_check_dag_builder.build_dag()
