---
name: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}

services:
  postgres:
    container_name: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}_postgres
    hostname: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}_postgres
    image: postgres:11
    env_file:
      - .env
    volumes:
      - ../data/postgresql:/var/lib/postgresql/data:rw
      - ./localtime:/etc/localtime:ro
    ports:
      - ${POSTGRES_EXPOSED_PORT}:${POSTGRES_PORT}
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "airflow"]
      interval: 5s
      retries: 5
    restart: always
    extra_hosts:
      - "host.docker.internal:host-gateway"
    networks:
      - custom_network
  airflow-webserver:
    container_name: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}_airflow_webserver
    restart: always
    image: ${CONTAINER_REGISTRY_BASE}/${BALTE_CONTAINER_IMAGE_PATH}
    command: ["/opt/conda/envs/${CONDA_ENV_NAME}/bin/airflow", "webserver"]
    env_file:
      - .env
    extra_hosts:
      - "host.docker.internal:host-gateway"
    volumes:
      - ./:/app/src:rw
      - ./config/airflow:/root/airflow:rw
      - ./exchange/dags:/opt/airflow/dags:rw
      - ../data/logs/airflow/:/opt/airflow/logs:rw
      - ./localtime:/etc/localtime:ro
    ports:
      - "${AIRFLOW_EXPOSED_PORT}:${AIRFLOW_PORT}"
    depends_on:
      - postgres
    networks:
      - custom_network
  airflow-scheduler:
    container_name: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}_airflow_scheduler
    restart: always
    image: ${CONTAINER_REGISTRY_BASE}/${BALTE_CONTAINER_IMAGE_PATH}
    entrypoint: ["/bin/sh", "-c"]
    command:
      - |
        chmod +x /opt/entrypoint.sh && 
        /opt/entrypoint.sh && 
        exec /opt/conda/envs/${CONDA_ENV_NAME}/bin/airflow scheduler
    env_file:
      - .env
    extra_hosts:
      - "host.docker.internal:host-gateway"
    volumes:
      - ./:/app/src:rw
      - ./config/airflow:/root/airflow:rw
      - ./exchange/dags:/opt/airflow/dags:rw
      - ../data/logs/airflow/:/opt/airflow/logs:rw
      - ./config/airflow/entrypoint.sh:/opt/entrypoint.sh
      - ../data/logs:/opt/balte_live/log:rw
      - ${LOCAL_PYCE_TOKEN_PATH}:/opt/balte_live/pyce_token:rw
      - ./localtime:/etc/localtime:ro
    depends_on:
      - postgres
    networks:
      - custom_network
  kafka:
    image: bitnami/kafka:3.7
    container_name: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}_kafka
    ports:
      - "${KAFKA_PLAINTEXT_PORT}:${KAFKA_PLAINTEXT_PORT}"
    restart: always
    networks:
      - custom_network
    env_file:
      - .env
    volumes:
      - ../data/kafka/:/bitnami/kafka
      - ./localtime:/etc/localtime:ro
    healthcheck:
      test: ["CMD", "kafka-topics.sh", "--bootstrap-server", "localhost:${KAFKA_PLAINTEXT_PORT}", "--list"]
      interval: 10s
      retries: 5
      start_period: 10s
      timeout: 5s

  kafka-ui:
    container_name: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}_kafka_ui
    profiles: ["frontend"]
    image: provectuslabs/kafka-ui:latest
    ports:
        - "${KAFKA_UI_EXPOSED_PORT}:${KAFKA_UI_PORT}"
    restart: always
    networks:
      - custom_network
    depends_on:
        - kafka
    env_file:
      - .env
    volumes:
      - ./localtime:/etc/localtime:ro
  
  oms:
    container_name: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}_oms
    image: ${CONTAINER_REGISTRY_BASE}/${OMS_CONTAINER_IMAGE_PATH}
    command: ["/opt/conda/envs/env_oms_0.0.3/bin/python", "runner.py", "${EXECUTION_LEVEL}", "--trading_universe", "${EXCHANGE_TYPE}", "--port", "${OMS_PORT}"]
    restart: always
    ports:
        - "${OMS_EXPOSED_PORT}:${OMS_PORT}"
    networks:
        - custom_network
    depends_on:
      kafka : 
        condition: service_started
      db:
        condition: service_healthy
    env_file:
      - .env
    volumes:
      - ../data/logs:/opt/balte_live/log:rw
      - ./localtime:/etc/localtime:ro

  oms_db_executor:
    container_name: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}_oms_db_executor
    image: ${CONTAINER_REGISTRY_BASE}/${OMS_CONTAINER_IMAGE_PATH}
    command: ["/opt/conda/envs/env_oms_0.0.3/bin/python", "db_executor_runner.py", "${EXECUTION_LEVEL}", "${EXCHANGE_TYPE}", "sync"]
    restart: always
    networks:
        - custom_network
    depends_on:
        - kafka
    env_file:
      - .env
    volumes:
      - ../data/logs/oms_db_executor:/opt/balte_live/log/oms_db_executor:rw
      - ./localtime:/etc/localtime:ro
  
  db:
    container_name: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}_mysql
    image: mysql:5.7
    command: --default-authentication-plugin=mysql_native_password
    restart: always
    networks:
        - custom_network
    volumes:
      - ./config/mysql-master/:/etc/mysql/conf.d/:rw
      - ./config/mysql-initdb:/docker-entrypoint-initdb.d:rw # Initial dump in mysql server
      - ../data/mysql:/var/lib/mysql
      - ./localtime:/etc/localtime:ro
    env_file:
      - .env
    ports:
        - "${MYSQL_EXPOSED_PORT}:${MYSQL_PORT}"
    healthcheck:
      interval: 30s
      retries: 10
      test: ["CMD", "/usr/bin/mysql",  "--user=${MYSQL_USER}",  "--password=${MYSQL_PASSWORD}",  "--execute",  "SHOW DATABASES;"]
      timeout: 5s

  minio:
    container_name: ${EXCHANGE_TYPE}-${EXECUTION_LEVEL}-minio
    restart: always
    hostname: ${EXCHANGE_TYPE}-${EXECUTION_LEVEL}-minio
    image: quay.io/minio/minio:RELEASE.2023-09-30T07-02-29Z
    volumes:
      - ../data/minio/data:/data
      - ./config/minio/lifecycle.json:/opt/lifecycle.json
      - ./config/minio/entrypoint.sh:/opt/entrypoint.sh
      - ./localtime:/etc/localtime:ro
    ports:
      - "${MINIO_CONSOLE_EXPOSED_PORT}:${MINIO_CONSOLE_PORT}"
      - "${MINIO_API_EXPOSED_PORT}:${MINIO_API_PORT}"
    networks:
        - custom_network
    env_file:
      - .env
    entrypoint: [ "/bin/sh", "-c", "chmod +x /opt/entrypoint.sh && /opt/entrypoint.sh" ]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${MINIO_API_PORT}/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
  exit_portal:
    container_name: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}_exit_portal
    restart: always
    hostname: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}_exit_portal
    image: ${CONTAINER_REGISTRY_BASE}/${BALTE_CONTAINER_IMAGE_PATH}
    entrypoint: [ "/bin/sh", "-c", "chmod +x /opt/entrypoint.sh && /opt/entrypoint.sh" ]
    ports:
      - "${EXIT_PORTAL_EXPOSED_PORT}:${EXIT_PORTAL_PORT}"
    networks:
        - custom_network
    env_file:
      - .env
    volumes:
      - .:/app/src:rw
      - ./localtime:/etc/localtime:ro
      - ./config/streamlit/entrypoint.sh:/opt/entrypoint.sh
      - ./config/streamlit/auth.yaml:/opt/auth.yaml
      - ../data/logs/exit_portal:/opt/exit_portal/log:rw
    depends_on:
      db:
        condition: service_healthy
      minio:
        condition: service_healthy

    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${EXIT_PORTAL_PORT}/_stcore/health"]
      interval: 30s
      timeout: 20s
      retries: 3

  live_bot:
    container_name: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}_live_bot
    restart: always
    hostname: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}_live_bot
    image: ${CONTAINER_REGISTRY_BASE}/${BALTE_CONTAINER_IMAGE_PATH}
    networks:
        - custom_network
    env_file:
      - .env
    volumes:
      - .:/app/src:ro
      - ./localtime:/etc/localtime:ro
    command: ["/opt/conda/envs/${CONDA_ENV_NAME}/bin/python","/app/src/exchange/apps/live_bot.py"]   


  alerts:
    container_name: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}_alerts
    restart: always
    image: ${CONTAINER_REGISTRY_BASE}/docker_alerts:latest
    entrypoint: ["/bin/sh", "-c"]
    command:
      - |
        sleep 30 && # false positives in the beginning as containers restart for example because ib-gateway is up but not logged in etc
        docker events --filter event=stop --filter event=die --filter label="com.docker.compose.project"=$COMPOSE_PROJECT_NAME --format \
          "{{.TimeNano}} {{.Type}} {{.Action}} {{.Actor.Attributes.name}} (id = {{.ID}})" | \
          while read line; do python3 /app/src/core/apps/send_alert.py "$$line"; done
    env_file:
      - .env
    volumes:
      - ./:/app/src:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./localtime:/etc/localtime:ro
    networks:
      - custom_network

  cron:
    container_name: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}_cron
    restart: unless-stopped
    image: docker:cli
    env_file:
      - .env
    volumes:
      - ./config/cron.txt:/root/cron.txt
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./localtime:/etc/localtime:ro
    command: sh -c "cat /root/cron.txt > /etc/crontabs/root && crond -f -d 8"
    networks:
      - custom_network

  alloy:
    container_name: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}_alloy
    image: grafana/alloy:v1.7.5
    restart: unless-stopped
    ports:
      - "${ALLOY_EXPOSED_PORT}:12345"
    volumes:
      - ./config/alloy/config.alloy:/etc/alloy/config.alloy
      - ../data/alloy:/var/lib/alloy/data
      - ../data/logs:/opt/logs:ro
      - ./localtime:/etc/localtime:ro
    command:
      - run
      - --server.http.listen-addr=0.0.0.0:12345
      - --storage.path=/var/lib/alloy/data
      - /etc/alloy/config.alloy
    networks:
      - custom_network

networks:
    custom_network:
        name: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}
        driver: bridge