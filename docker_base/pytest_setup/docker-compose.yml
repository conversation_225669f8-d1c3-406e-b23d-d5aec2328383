version: '3.8'

networks:
  test_network:
    driver: bridge

services:
  mysql:
    image: mysql:5.7
    container_name: mysql_test
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: "test_db"
      MYSQL_ROOT_PASSWORD: "test_pwd"
      MYSQL_USER: "test_user"
      MYSQL_PASSWORD: "test_pwd"
    ports:
      - "3396:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - test_network

  test_runner:
    build:
      context: ".."
      dockerfile: "pytest_setup/Dockerfile"
    container_name: test_runner
    depends_on:
      - mysql
    environment:
      PYTHONPATH: "/opt/build:/opt/build/src:/opt/build/pipeline:/app/balte_live"
      MYSQL_HOST: "mysql"
      MYSQL_DATABASE: "test_db"
      MYSQL_USER: "test_user"
      MYSQL_PASSWORD: "test_pwd"
      CI_JOB_STAGE: "test"
    working_dir: /app/docker_infra
    entrypoint: ["/bin/bash"]
    stdin_open: true
    tty: true
    networks:
      - test_network

volumes:
  mysql_data: