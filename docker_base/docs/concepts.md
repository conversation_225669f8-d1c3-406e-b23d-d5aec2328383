# Docker Base Module Overview

The **Docker Base Module** supports essential infrastructure and scripts for running **Airflow DAGs**, **Streamlit applications**, and provides configuration files for basic Docker services such as **MySQL**, **Airflow**, and **Streamlit**.

## Airflow DAGs Design

This module employs a structured approach for managing Airflow DAGs:

### `DagBase` Class
- At the core of each DAG is the `DagBase` class, which defines an abstract method, `build_dag`.
- Each DAG script inherits from `DagBase` and implements a DAG-specific base class located in the `core` folder.

### DAG Construction
- The base class in `core` is further inherited by a DAG class in the `exchange` folder.
- In the `exchange` folder, the DAG instance is constructed by passing necessary arguments. The `build_dag` method is invoked and assigned to a global variable, making it available for Airflow to run.

### Task Execution in DAGs
- **Python Operator DAGs**: Logic for DAGs that directly execute Python code can be implemented within their respective classes.
- **Bash Operator DAGs**: DAGs that execute external scripts are categorized as **runners**.

```bash
docker_base/
│
├── core/
│   ├── dags/
│   │   └── **
|   |       └── specific_dag_base.py  # Base class for a Dag inheriting the DagBase class
│   │   └── **
|   |       └── ...  # Base classes for other dags
│
└── exchange/
    └── dags/
        └── **
           └── specific_dag.py # Class for a Dag inheriting the Base class of this dag defined in the core folder
        └── **
           └── ... # Classes for other dags inheriting their respective base classes
```

## Runners Design

External scripts called by DAGs are organized as **runners**:

### `RunnerBase` Interface
- Each runner class inherits from `RunnerBase`, an interface that mandates a `run` method. Each runner script has a base class in the `core` folder.

### Runner Class Construction
- Similar to DAG setup, each runner class in the `core` folder is inherited by a class in the `exchange` folder, where the `run` method is called.
- This method is executed when a DAG invokes the runner using the Bash Operator.

### Additional Runner Usages
- Beyond Airflow tasks, runners are used to execute other Python scripts, such as those required for operating Streamlit applications.

```bash
docker_base/
│
├── core/
│   ├── runners/
│   │   └── **
|   |       └── specific_runner_base.py  # Base class for a Runner inheriting the RunnerBase class
│   │   └── **
|   |       └── ...  # Base classes for other runners
│
└── exchange/
    └── runners/
        └── **
           └── specific_runner.py # Class for a runner inheriting the Base class of this runner defined in the core folder
        └── **
           └── ... # Classes for other runners inheriting their respective base classes
```

## Streamlit App Design
The design for Streamlit applications follows a similar structure to that of the runners:

### Streamlit App Runner
A StreamlitAppRunner base class is implemented in the `core/apps` folder, inheriting from the basic runner interface. The actual class, whose run method is invoked, is implemented in the `exchange/app` directory. Each Streamlit app consists of multiple pages, where each page extends the runner interface, adhering to the same design principles as the other runners.