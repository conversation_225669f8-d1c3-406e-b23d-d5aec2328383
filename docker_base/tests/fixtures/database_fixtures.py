import pymysql
import pytest
from core.helpers.configstore import (
    MYSQL_HOST,
    MYSQL_USER,
    MYSQL_PASSWORD,
    DB_NAME,
)
import pandas as pd


@pytest.fixture
def table_setup(request):
    """
    A pytest fixture to insert test data into specified MySQL tables before running tests
    and clean up after tests are completed.

    This fixture:
    - Takes `request.param`, a dictionary with:
      - `"name"`: A list of table names.
      - `"value"`: A list of corresponding pandas DataFrames containing data for insertion.
    - Inserts the provided data into the specified tables.
    - Yields control to execute the test.
    - After the test completes, it truncates (clears) the inserted tables.

    Args:
        request: A pytest request object containing table names and DataFrames.

    Raises:
        AssertionError: If `request.param` is not a dictionary with valid `"name"` and `"value"` lists.

    Example Usage:
        @pytest.mark.parametrize(
            "table_setup",
            [{"name": ["table1", "table2"], "value": [df1, df2]}],
            indirect=True,
        )
        def test_something(table_setup):
            # Test logic here...

    Notes:
        - This fixture should be used with `pytest.mark.parametrize` with `indirect=True`.
        - The DataFrame columns must match the table schema in MySQL.
    """
    assert isinstance(request.param, dict)
    assert ("name" in request.param) and isinstance(request.param.get("name"), list)
    assert ("value" in request.param) and isinstance(request.param.get("value"), list)
    table_names = request.param.get("name")
    table_dfs = request.param.get("value")
    assert len(table_names) == len(table_dfs)

    # Add stuff to table here
    with pymysql.connect(
        host=MYSQL_HOST,
        user=MYSQL_USER,
        password=MYSQL_PASSWORD,
        db=DB_NAME,
        charset="utf8mb4",
        cursorclass=pymysql.cursors.DictCursor,
    ) as connection:
        with connection.cursor() as cursor:
            for i in range(len(table_names)):
                df = table_dfs[i]
                table = table_names[i]
                for _, row in df.iterrows():
                    # Replace NaN with None for SQL compatibility
                    values = [None if pd.isna(value) else value for value in row]

                    # Build the INSERT query dynamically to match table columns
                    placeholders = ", ".join(["%s"] * len(row))
                    query = f"INSERT INTO {table} ({', '.join(df.columns)}) VALUES ({placeholders})"
                    cursor.execute(query, values)

        # Commit the changes
        connection.commit()

    yield
    # Truncate table here
    with pymysql.connect(
        host=MYSQL_HOST,
        user=MYSQL_USER,
        password=MYSQL_PASSWORD,
        db=DB_NAME,
        charset="utf8mb4",
        cursorclass=pymysql.cursors.DictCursor,
    ) as connection:
        with connection.cursor() as cursor:
            for table in table_names:
                cursor.execute(f"TRUNCATE TABLE {table}")
        # Commit the changes
        connection.commit()
