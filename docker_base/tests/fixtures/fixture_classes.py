from mockafka.aiokafka import FakeAIOKafkaConsumer
import base64
from typing import Optional, Dict, List, Any
from toti.rpc import oms_pb2
from google.protobuf.json_format import Parse
from aiokafka.structs import (  # type: ignore[import-untyped]
    ConsumerRecord,
    TopicPartition,
)
from core.apps.exit_portal_components.exit_positions_op import ExitPositionOperationBase
from core.apps.exit_portal_components.move_live_to_dead_op import (
    MoveLiveToDeadOperationBase,
)
from core.runners.oms_db_copy_base import OMSDBCopyRunnerBase
from core.helpers.mixins import TradeQueryMixinBase
from core.dags.monitoring.trading_checks_builder_base import (
    TradingCheckDagBuilderBase,
)
from core.dags.monitoring.strat_wise_time_delta_builder_base import (
    StratWiseTimeDeltaBuilderBase,
)
from core.dags.monitoring.position_mismatch_builder_base import (
    PositionMismatchDagBuilderBase,
)
from core.dags.monitoring.older_trade_alert_builder_base import (
    OlderTradeAlertDagBuilderBase,
)
from core.dags.trading.balte_trading_builder_base import (
    BalteTradingDagBuilderBase,
)


class BalteTradingDagBuilder(BalteTradingDagBuilderBase):
    """
    Mock class to be used for pytests to define a `BalteTradingDagBuilderBase` instance.
    """

    pass


class OlderTradeAlertDagBuilder(OlderTradeAlertDagBuilderBase):
    """
    Mock class to be used for pytests to define a `OlderTradeAlertDagBuilderBase` instance.
    """

    pass


class PositionMismatchDagBuilder(PositionMismatchDagBuilderBase):
    """
    Mock class to be used for pytests to define a `PositionMismatchDagBuilderBase` instance.
    """

    pass


class StratWiseTimeDeltaBuilder(StratWiseTimeDeltaBuilderBase):
    """
    Mock class to be used for pytests to define a `StratWiseTimeDeltaBuilderBase` instance.
    """

    pass


class TradingCheckDagBuilder(TradingCheckDagBuilderBase):
    """
    Mock class to be used for pytests to define a `TradingCheckDagBuilderBase` instance.
    """

    pass


class OMSDBCopyRunner(OMSDBCopyRunnerBase, TradeQueryMixinBase):
    """
    Mock class to be used for pytests to define a `OMSDBCopyRunnerBase` instance.
    """

    pass


class MoveLiveToDeadOperation(MoveLiveToDeadOperationBase):
    """
    Mock class to be used for pytests to define a `MoveLiveToDeadOperationBase` instance.
    """

    pass


class ExitPositionOperation(ExitPositionOperationBase):
    """
    Mock class to be used for pytests to define a `ExitPositionOperationBase` instance.
    """

    pass


class MockAIOKafkaConsumer(FakeAIOKafkaConsumer):
    """
    A mock class for the AIOKafkaConsumer to simulate fetching and decoding Kafka messages.
    """

    def __init__(self, *topics: str, **kwargs: Any) -> None:
        super().__init__(*topics, **kwargs)
        self.fake_consumer: Optional[FakeAIOKafkaConsumer] = None

    @staticmethod
    def get_serialized_message_string(raw_trade_message: str) -> str:
        """
        Converts a raw trade message (JSON string) into a base64-encoded serialized protobuf message.

        Args:
            raw_trade_message (str): The raw trade message in JSON string format.

        Returns:
            str: The base64-encoded serialized protobuf message.

        Raises:
            google.protobuf.json_format.ParseError: If the input JSON cannot be parsed into the protobuf message.
        """
        log_message: oms_pb2.LogMessage = Parse(raw_trade_message, oms_pb2.LogMessage())
        return base64.b64encode(log_message.SerializeToString()).decode("utf-8")

    async def getmany(
        self,
        *partitions: TopicPartition,
        timeout_ms: int = 0,
        max_records: Optional[int] = None,
    ) -> Dict[TopicPartition, List[ConsumerRecord[bytes, bytes]]]:
        """
        Mock method to simulate fetching Kafka messages and decoding them from base64.

        Args:
            *partitions: Topic partitions to fetch messages from.
            timeout_ms: Timeout in milliseconds.
            max_records: Maximum number of records to fetch.

        Returns:
            A dictionary with TopicPartition as keys and a list of ConsumerRecord as values.
        """
        if self.fake_consumer is None:
            self.fake_consumer = FakeAIOKafkaConsumer()
            self.fake_consumer.subscribe(["test_nse_async_msgs_oms"])
            await self.fake_consumer.start()
        message_batch = await self.fake_consumer.getmany()

        for _, messages in message_batch.items():
            for message in messages:
                message.value = base64.b64decode(message.value)
        return message_batch
