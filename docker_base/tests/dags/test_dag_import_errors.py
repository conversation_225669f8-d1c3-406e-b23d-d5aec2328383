# type: ignore
import pytest
from airflow.models import DagBag


def test_dag_import_errors(monkeypatch) -> None:
    # so that it doesn't use analytics api in pipeline
    monkeypatch.setattr(
        "core.helpers.utils.get_live_strats",
        lambda *args, **kwargs: ["dummy_strat1", "dummy_strat2"],
    )

    dagbag = DagBag(dag_folder="/root/airflow_dags", include_examples=False)
    assert (
        len(dagbag.import_errors) == 0
    ), f"Dags have the following import errors: {dagbag.import_errors}"
