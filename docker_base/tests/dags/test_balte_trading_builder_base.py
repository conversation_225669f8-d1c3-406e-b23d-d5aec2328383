# type: ignore
import pytest
from typing import List
from core.helpers.configstore import (
    EXCHANGE_TYPE,
    EXECUTION_LEVEL,
)


@pytest.mark.parametrize(
    "task_count, task_ids",
    [
        (
            6,
            [
                "trading_holiday_check",
                "trading_day",
                "holiday",
                f"{EXCHANGE_TYPE}_{EXECUTION_LEVEL}_SLAVE",
                f"{EXCHANGE_TYPE}_{EXECUTION_LEVEL}_WATCHDOG",
                f"{EXCHANGE_TYPE}_{EXECUTION_LEVEL}_CLUSTER",
            ],
        )
    ],
)
def test_dag_structure(balte_trading_dag_builder, task_count: int, task_ids: List[str]):
    # check for import errors by building dag here
    dag = balte_trading_dag_builder.build_dag()
    assert dag.dag_id == "BaLTE_Trading_Infra"

    assert (
        len(dag.tasks) == task_count
    ), f"Expected {task_count} tasks in DAG '{dag.dag_id}', found {len(dag.tasks)}"

    for task_id in task_ids:
        assert task_id in dag.task_dict, f"Task with id: {task_id} not present in dag"

    assert len(dag.task_dict["trading_day"].downstream_task_ids) == 3
    assert len(dag.task_dict["holiday"].downstream_task_ids) == 0
