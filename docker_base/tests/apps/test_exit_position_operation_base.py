# type: ignore
import pytest
import pandas as pd
from test_data.datastore import SA<PERSON>LE_LIVE_TRADES
from core.helpers.configstore import (
    EXCHANGE_TYPE,
    EXECUTION_LEVEL,
)


TABLE_SETUP_PARAM = {
    "name": [f"{EXCHANGE_TYPE}_{EXECUTION_LEVEL}"],
    "value": [pd.DataFrame(SAMPLE_LIVE_TRADES)],
}


@pytest.mark.parametrize("table_setup", [TABLE_SETUP_PARAM], indirect=True)
def test_exit_positions_with_strategy_name(
    monkeypatch, table_setup, exit_position_operation
):
    def mock_send_exit_trades(*args, **kwargs):
        pass

    monkeypatch.setattr(
        "core.helpers.mixins.OmsOpsMixinBase.send_exit_trades",
        mock_send_exit_trades,
    )

    assert (
        exit_position_operation.exit_positions_with_strategy_name(
            "cluster_strat", pd.Timestamp.now()
        )
        == 1
    )


@pytest.mark.parametrize("table_setup", [TABLE_SETUP_PARAM], indirect=True)
def test_exit_positions_with_symbol(monkeypatch, table_setup, exit_position_operation):
    def mock_send_exit_trades(*args, **kwargs):
        pass

    monkeypatch.setattr(
        "core.helpers.mixins.OmsOpsMixinBase.send_exit_trades",
        mock_send_exit_trades,
    )

    assert (
        exit_position_operation.exit_positions_with_symbol("TEST", pd.Timestamp.now())
        == 2
    )


@pytest.mark.parametrize("table_setup", [TABLE_SETUP_PARAM], indirect=True)
def test_exit_positions_with_segment(monkeypatch, table_setup, exit_position_operation):
    def mock_send_exit_trades(*args, **kwargs):
        pass

    monkeypatch.setattr(
        "core.helpers.mixins.OmsOpsMixinBase.send_exit_trades",
        mock_send_exit_trades,
    )

    assert (
        exit_position_operation.exit_positions_with_segment(
            "OPTIDX", pd.Timestamp.now()
        )
        == 2
    )


@pytest.mark.parametrize("table_setup", [TABLE_SETUP_PARAM], indirect=True)
def test_exit_positions_with_trade_ids(
    monkeypatch, table_setup, exit_position_operation
):
    def mock_send_exit_trades(*args, **kwargs):
        pass

    monkeypatch.setattr(
        "core.helpers.mixins.OmsOpsMixinBase.send_exit_trades",
        mock_send_exit_trades,
    )

    assert exit_position_operation.exit_positions_with_trade_ids(
        ["1"], pd.Timestamp.now()
    ) == ["1"]
