from core.helpers.one_second_exit_application_components.structs import (
    OneSecondExitPosition,
    OrderStopLossInformation,
)
import pytest
from mockafka import aproduce, asetup_kafka
import pandas as pd
from balte.struct import OrderStopLossInfo
from test_data.datastore import KAFKA_TRADE_MESSAGES
from fixtures.fixture_classes import MockAIOKafkaConsumer


@pytest.mark.asyncio
async def test_create_kafka_consumer(custom_kafka_processor):
    await custom_kafka_processor.create_kafka_consumer()
    assert custom_kafka_processor.consumer is not None
    assert isinstance(custom_kafka_processor.consumer, MockAIOKafkaConsumer)


@pytest.mark.asyncio
@asetup_kafka(topics=[{"topic": "test_nse_async_msgs_oms", "partition": 1}])
@aproduce(
    topic="test_nse_async_msgs_oms",
    value=MockAIOKafkaConsumer.get_serialized_message_string(
        raw_trade_message=KAFKA_TRADE_MESSAGES["order_entry"]
    ).encode("utf-8"),
    partition=0,
)
@aproduce(
    topic="test_nse_async_msgs_oms",
    value=MockAIOKafkaConsumer.get_serialized_message_string(
        raw_trade_message=KAFKA_TRADE_MESSAGES["order_exit"]
    ).encode("utf-8"),
    partition=0,
)
async def test_read_from_kafka_and_process(custom_kafka_processor):
    await custom_kafka_processor.read_from_kafka_and_process()
    assert len(custom_kafka_processor.position_manager.trade_ids_of_dead_trades) == 1
    assert custom_kafka_processor.position_manager.trade_ids_of_dead_trades == set(
        [827]
    )
    assert custom_kafka_processor.position_manager.one_second_exit_positions[
        826
    ] == OneSecondExitPosition(
        TRADEID=826,
        SYMBOL="NIFTY",
        QUANTITY=-600,
        STRATEGY="elva_v2",
        ENTRY_TIMESTAMP=pd.Timestamp("2025-03-25 21:36:14"),
        SEGMENT="OPTIDX",
        EXPIRY="25-Mar-2025",
        TYPE="CE",
        STRIKE=22550.0,
        ENTRY_PRICE=1.2000000476837158,
        ORDER_TYPE="NORMAL",
        ENTRY_TIMESTAMP_META=pd.Timestamp("2025-03-25 21:36:00"),
        SLAVE_NAME="default_slave",
        EXTENDED_INFO='{"order_stoploss_info":[{"universe":"opt_onemin","balte_id":225002025032515001,"price":0.6000000238418579,"operator":"<=","exit_on_1min":true},{"universe":"opt_onemin","balte_id":225002025032515001,"price":1.8000000715255737,"operator":">="}],"serialized_meta_obj":""}',
    )
    assert custom_kafka_processor.position_manager.one_sec_stoploss_info_list == [
        OrderStopLossInformation(
            trade_id=826,
            order_stoploss_info=OrderStopLossInfo(
                universe="opt_onemin",
                balte_id=225002025032515001,
                price=1.8000000715255737,
                operator=">=",
                exit_on_1min=False,
            ),
        )
    ]
    assert custom_kafka_processor.position_manager.one_min_stoploss_info_list == [
        OrderStopLossInformation(
            trade_id=826,
            order_stoploss_info=OrderStopLossInfo(
                universe="opt_onemin",
                balte_id=225002025032515001,
                price=0.6000000238418579,
                operator="<=",
                exit_on_1min=True,
            ),
        )
    ]
