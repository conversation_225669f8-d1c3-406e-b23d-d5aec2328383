from exchange.helpers.one_second_exit_application_components.data_client import (
    OneSecondExitApplicationDataClient,
)
import pytest
import logging


@pytest.mark.asyncio
async def test_fetch_data():
    one_second_application_data_client: OneSecondExitApplicationDataClient = (
        OneSecondExitApplicationDataClient(logger=logging.getLogger())
    )
    with pytest.raises(NotImplementedError):
        await one_second_application_data_client.fetch_data(active_balte_ids=[])
