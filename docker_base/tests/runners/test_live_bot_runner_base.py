# type: ignore
import pytest
from core.runners.live_bot_runner_base import RunnerLiveBotBase

alert_list = []


@pytest.mark.asyncio
async def test_process_balte_messages(mocker, mock_kafka_consumer):
    # Set up test message

    def mock_send_message_to_gspace(msg, url):
        global alert_list
        alert_list.append(msg)
        return

    mocker.patch(
        "core.runners.live_bot_runner_base.send_message_to_gspace",
        mock_send_message_to_gspace,
    )

    # Test the process_balte_messages function
    await RunnerLiveBotBase.process_balte_messages(mock_kafka_consumer)

    global alert_list
    assert alert_list[0] == "nse_test_SLAVE Module initialize ran successfully"
    assert (
        alert_list[1][20:]
        == " INFO krx_production_CLUSTERModule:Order RejectedEntryTradeFromOMS for strategy: cluster_korea, balte_id: 37252024051318001 returning None"
    )
    assert (
        alert_list[2][20:]
        == " ERROR krx_production_CLUSTERModule:Order RejectedEntryTradeFromOMS for strategy: cluster_korea, balte_id: 37252024051308001 returning None"
    )
    # Check that the consumer iterator was used to get the message
    mock_kafka_consumer.__aiter__.assert_called_once()
