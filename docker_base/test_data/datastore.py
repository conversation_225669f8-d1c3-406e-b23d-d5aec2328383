import pandas as pd
import json
import datetime

SAMPLE_LIVE_TRADES = {
    "TRADEID": [1, 2],
    "SYMBOL": ["TEST", "TEST"],
    "QUANTITY": [1, 1],
    "STRATEGY": ["BaLTE_test_slave_strat", "cluster_strat"],
    "ENTRY_TIMESTAMP": [
        pd.Timestamp("Dec 19, 2023, 9:30 AM"),
        pd.Timestamp("Dec 19, 2023, 9:30 AM"),
    ],
    "SEGMENT": ["OPTIDX", "OPTIDX"],
    "EXPIRY": ["21-Dec-2023", "21-Dec-2023"],
    "TYPE": ["CE", "CE"],
    "STRIKE": [345.5, 345.5],
    "ENTRY_PRICE": [0.77, 0.77],
    "ORDER_TYPE": ["NORMAL", "NORMAL"],
    "ENTRY_TIMESTAMP_META": [
        pd.Timestamp("Dec 19, 2023, 9:30 AM"),
        pd.Timestamp("Dec 19, 2023, 9:30 AM"),
    ],
    "SLAVE_NAME": ["default_slave", "test_slave_strat"],
    "EXTENDED_INFO": ["", ""],
}

SAMPLE_DEAD_TRADES = {
    "TRADEID": [3, 4],
    "SYMBOL": ["TEST", "TEST"],
    "QUANTITY": [1, 1],
    "STRATEGY": ["BaLTE_test_slave_strat", "cluster_strat"],
    "ENTRY_TIMESTAMP": [
        pd.Timestamp("Dec 19, 2023, 9:30 AM"),
        pd.Timestamp("Dec 19, 2023, 9:30 AM"),
    ],
    "EXIT_TIMESTAMP": [
        pd.Timestamp("Dec 19, 2023, 10:30 AM"),
        pd.Timestamp("Dec 19, 2023, 12:30 PM"),
    ],
    "SEGMENT": ["OPTIDX", "OPTIDX"],
    "EXPIRY": ["21-Dec-2023", "21-Dec-2023"],
    "TYPE": ["CE", "CE"],
    "STRIKE": [345.5, 345.5],
    "ENTRY_PRICE": [0.77, 0.77],
    "EXIT_PRICE": [0.6, 0.8],
    "ORDER_TYPE": ["NORMAL", "NORMAL"],
    "ENTRY_TIMESTAMP_META": [
        pd.Timestamp("Dec 19, 2023, 9:30 AM"),
        pd.Timestamp("Dec 19, 2023, 9:30 AM"),
    ],
    "EXIT_TIMESTAMP_META": [
        pd.Timestamp("Dec 19, 2023, 10:30 AM"),
        pd.Timestamp("Dec 19, 2023, 12:30 PM"),
    ],
    "SLAVE_NAME": ["default_slave", "test_slave_strat"],
    "EXTENDED_INFO": ["", ""],
}

KAFKA_TRADE_MESSAGES = {
    "order_entry": """
    {
    "logType": "ORDER_ENTRY",
    "timestamp_bar": "2025-03-25 21:36:00",
    "entryRequest": {
        "trade_id": 826,
        "symbol": "NIFTY",
        "quantity": -600,
        "strategy": "elva_v2",
        "entry_timestamp": "2025-03-25 21:36:14",
        "segment": "OPTIDX",
        "expiry": "25-Mar-2025",
        "type": "CE",
        "strike": 22550.0,
        "entry_price": 1.2000000476837158,
        "order_type": "NORMAL",
        "entry_timestamp_meta": "2025-03-25 21:36:00",
        "slave_name": "default_slave",
        "extended_info": "{\\"order_stoploss_info\\":[{\\"universe\\":\\"opt_onemin\\",\\"balte_id\\":225002025032515001,\\"price\\":0.6000000238418579,\\"operator\\":\\"<=\\",\\"exit_on_1min\\":true},{\\"universe\\":\\"opt_onemin\\",\\"balte_id\\":225002025032515001,\\"price\\":1.8000000715255737,\\"operator\\":\\">=\\"}],\\"serialized_meta_obj\\":\\"\\"}"
    }
    }
    """,
    "order_exit": """
    {
    "logType": "ORDER_EXIT",
    "timestamp_bar": "2025-03-25 21:36:00",
    "exitRequest": {
        "trade_id": 827,
        "symbol": "NIFTY",
        "quantity": -600,
        "strategy": "elva_v2",
        "exit_timestamp": "2025-03-25 21:36:23",
        "segment": "OPTIDX",
        "expiry": "25-Mar-2025",
        "type": "CE",
        "strike": 22550.0,
        "exit_price": 1.2000000476837158,
        "order_type": "NORMAL",
        "exit_timestamp_meta": "2025-03-25 21:36:00",
        "slave_name": "default_slave",
        "extended_info": "{\\"order_stoploss_info\\":[{\\"universe\\":\\"opt_onemin\\",\\"balte_id\\":225002025032515001,\\"price\\":0.6000000238418579,\\"operator\\":\\"<=\\",\\"exit_on_1min\\":true},{\\"universe\\":\\"opt_onemin\\",\\"balte_id\\":225002025032515001,\\"price\\":1.8000000715255737,\\"operator\\":\\">=\\",\\"exit_on_1min\\":true}],\\"serialized_meta_obj\\":\\"\\"}"
    }
    }
    """,
}

LIVE_TRADES_TO_INSERT = {
    "TRADEID": [77, 79],
    "SYMBOL": ["NIFTY", "NIFTY"],
    "QUANTITY": [75, 75],
    "STRATEGY": ["BaLTE_ravat_jordan_bn_month", "BaLTE_ravat_jordan_bn_month"],
    "ENTRY_TIMESTAMP": [
        pd.Timestamp("2025-03-25 15:15:04"),
        pd.Timestamp("2025-03-25 15:17:04"),
    ],
    "SEGMENT": ["OPTIDX", "OPTIDX"],
    "EXPIRY": ["27-Mar-2025", "27-Mar-2025"],
    "TYPE": ["PE", "PE"],
    "STRIKE": [22500, 22600],
    "ENTRY_PRICE": [16.575, 18.21],
    "ORDER_TYPE": ["NORMAL", "NORMAL"],
    "ENTRY_TIMESTAMP_META": [
        pd.Timestamp("2025-03-25 15:15:00"),
        pd.Timestamp("2025-03-25 15:17:00"),
    ],
    "SLAVE_NAME": ["default_slave", "default_slave"],
    "EXTENDED_INFO": [
        json.dumps(
            {
                "hedge_of_order_id": 76,
                "order_stoploss_info": [
                    {
                        "universe": "opt_onemin",
                        "balte_id": 225002025032705001,
                        "price": 198.75,
                        "operator": ">=",
                    },
                    {
                        "fixed_exit_timestamp": datetime.datetime(
                            2025, 3, 25, 15, 24
                        ).isoformat(),
                    },
                ],
                "serialized_meta_obj": "",
            }
        ),
        json.dumps(
            {
                "hedge_of_order_id": 78,
                "order_stoploss_info": [
                    {
                        "universe": "opt_onemin",
                        "balte_id": 226002025032705001,
                        "price": 218.75,
                        "operator": ">=",
                    },
                    {
                        "universe": "futidx_onemin",
                        "balte_id": 5001,
                        "price": 22634.75,
                        "operator": ">=",
                        "exit_on_1min": True,
                    },
                    {
                        "fixed_exit_timestamp": datetime.datetime(
                            2025, 3, 25, 15, 29
                        ).isoformat(),
                    },
                ],
                "serialized_meta_obj": "",
            }
        ),
    ],
}

DEAD_TRADES_TO_INSERT = {
    "TRADEID": [73, 75],
    "SYMBOL": ["NIFTY", "NIFTY"],
    "QUANTITY": [-300, -300],
    "STRATEGY": ["elva_v2", "elva_v2"],
    "ENTRY_TIMESTAMP": [
        pd.Timestamp("2025-03-21 16:40:06"),
        pd.Timestamp("2025-03-21 16:42:04"),
    ],
    "EXIT_TIMESTAMP": [
        pd.Timestamp("2025-03-21 16:57:00"),
        pd.Timestamp("2025-03-21 16:57:00"),
    ],
    "SEGMENT": ["OPTIDX", "OPTIDX"],
    "EXPIRY": ["21-Mar-2025", "21-Mar-2025"],
    "TYPE": ["CE", "CE"],
    "STRIKE": [5650, 5650],
    "ENTRY_PRICE": [5.7, 5.9],
    "EXIT_PRICE": [5.7, 5.9],
    "ORDER_TYPE": ["NORMAL", "NORMAL"],
    "ENTRY_TIMESTAMP_META": [
        pd.Timestamp("2025-03-21 16:40:00"),
        pd.Timestamp("2025-03-21 16:42:00"),
    ],
    "EXIT_TIMESTAMP_META": [
        pd.Timestamp("2025-03-21 17:00:00"),
        pd.Timestamp("2025-03-21 17:00:00"),
    ],
    "SLAVE_NAME": ["default_slave", "default_slave"],
    "EXTENDED_INFO": [
        json.dumps(
            {
                "order_stoploss_info": [
                    {
                        "universe": "opt_onemin",
                        "balte_id": 56502025032115001,
                        "price": 2.8499999046325684,
                        "operator": "<=",
                        "exit_on_1min": True,
                    },
                    {
                        "universe": "opt_onemin",
                        "balte_id": 56502025032115001,
                        "price": 8.549999713897705,
                        "operator": ">=",
                        "exit_on_1min": True,
                    },
                ],
                "serialized_meta_obj": "",
            }
        ),
        json.dumps(
            {
                "order_stoploss_info": [
                    {
                        "universe": "opt_onemin",
                        "balte_id": 56502025032115001,
                        "price": 2.950000047683716,
                        "operator": "<=",
                        "exit_on_1min": True,
                    },
                    {
                        "universe": "opt_onemin",
                        "balte_id": 56502025032115001,
                        "price": 8.850000143051147,
                        "operator": ">=",
                        "exit_on_1min": True,
                    },
                ],
                "serialized_meta_obj": "",
            }
        ),
    ],
}
