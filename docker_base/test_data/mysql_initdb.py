# type: ignore
import pymysql
import os
import time


MYSQL_HOST = os.environ["MYSQL_HOST"]
MYSQL_PASSWORD = os.environ["MYSQL_PASSWORD"]
MYSQL_USER = os.environ["MYSQL_USER"]
EXCHANGE_TYPE = os.environ["EXCHANGE_TYPE"]
EXECUTION_LEVEL = os.environ["EXECUTION_LEVEL"]

# SQL queries to create the database and tables
create_db_query = "CREATE DATABASE IF NOT EXISTS test_db"

create_unit_test_table = f"""
CREATE TABLE IF NOT EXISTS {EXCHANGE_TYPE}_{EXECUTION_LEVEL} (
    TRADEID INT(11) DEFAULT NULL,
    SYMBOL VARCHAR(15) DEFAULT NULL,
    QUANTITY INT(11) DEFAULT NULL,
    STRATEGY VARCHAR(60) DEFAULT NULL,
    ENTRY_TIMESTAMP DATETIME DEFAULT NULL,
    SEGMENT VARCHAR(15) DEFAULT NULL,
    EXPIRY VARCHAR(30) DEFAULT NULL,
    TYPE VARCHAR(15) DEFAULT NULL,
    STRIKE FLOAT DEFAULT NULL,
    ENTRY_PRICE FLOAT DEFAULT NULL,
    ORDER_TYPE VARCHAR(15) DEFAULT NULL,
    ENTRY_TIMESTAMP_META DATETIME DEFAULT NULL,
    SLAVE_NAME VARCHAR(255) DEFAULT NULL,
    EXTENDED_INFO VARCHAR(1023)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
"""

create_dead_trade_unit_test_table = f"""
CREATE TABLE IF NOT EXISTS dead_trade_{EXCHANGE_TYPE}_{EXECUTION_LEVEL} (
    TRADEID INT(11) DEFAULT NULL,
    SYMBOL VARCHAR(15) DEFAULT NULL,
    QUANTITY INT(11) DEFAULT NULL,
    STRATEGY VARCHAR(60) DEFAULT NULL,
    ENTRY_TIMESTAMP DATETIME DEFAULT NULL,
    SEGMENT VARCHAR(15) DEFAULT NULL,
    EXPIRY VARCHAR(15) DEFAULT NULL,
    TYPE VARCHAR(15) DEFAULT NULL,
    STRIKE FLOAT DEFAULT NULL,
    ENTRY_PRICE FLOAT DEFAULT NULL,
    ORDER_TYPE VARCHAR(15) DEFAULT NULL,
    EXIT_TIMESTAMP DATETIME DEFAULT NULL,
    EXIT_PRICE FLOAT DEFAULT NULL,
    ENTRY_TIMESTAMP_META DATETIME DEFAULT NULL,
    EXIT_TIMESTAMP_META DATETIME DEFAULT NULL,
    SLAVE_NAME VARCHAR(255) DEFAULT NULL,
    EXTENDED_INFO VARCHAR(1023) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
"""

create_dead_trade_unit_test_total_table = f"""
CREATE TABLE IF NOT EXISTS dead_trade_{EXCHANGE_TYPE}_{EXECUTION_LEVEL}_total (
    TRADEID INT(11) DEFAULT NULL,
    SYMBOL VARCHAR(15) DEFAULT NULL,
    QUANTITY INT(11) DEFAULT NULL,
    STRATEGY VARCHAR(60) DEFAULT NULL,
    ENTRY_TIMESTAMP DATETIME DEFAULT NULL,
    SEGMENT VARCHAR(15) DEFAULT NULL,
    EXPIRY VARCHAR(15) DEFAULT NULL,
    TYPE VARCHAR(15) DEFAULT NULL,
    STRIKE FLOAT DEFAULT NULL,
    ENTRY_PRICE FLOAT DEFAULT NULL,
    ORDER_TYPE VARCHAR(15) DEFAULT NULL,
    EXIT_TIMESTAMP DATETIME DEFAULT NULL,
    EXIT_PRICE FLOAT DEFAULT NULL,
    ENTRY_TIMESTAMP_META DATETIME DEFAULT NULL,
    EXIT_TIMESTAMP_META DATETIME DEFAULT NULL,
    SLAVE_NAME VARCHAR(255) DEFAULT NULL,
    EXTENDED_INFO VARCHAR(1023) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
"""

RETRY_COUNT = 5

while True:
    try:
        with pymysql.connect(
            host=MYSQL_HOST,
            user=MYSQL_USER,
            password=MYSQL_PASSWORD,
            cursorclass=pymysql.cursors.DictCursor,
        ) as connection:
            with connection.cursor() as cursor:
                # Create the database
                cursor.execute(create_db_query)
                print("Database 'test_db' created or already exists.")

                # Select the database
                cursor.execute("USE test_db")

                # Create the tables
                cursor.execute(create_unit_test_table)
                print("Table 'nse_test' created.")

                cursor.execute(create_dead_trade_unit_test_table)
                print("Table 'dead_trade_nse_test' created.")

                cursor.execute(create_dead_trade_unit_test_total_table)
                print("Table 'dead_trade_nse_test_total' created.")

            # Commit the changes
            connection.commit()
        break
    except Exception as e:
        print(f"Received Exception while initializing db: {repr(e)}")
        RETRY_COUNT -= 1
        if RETRY_COUNT < 0:
            break
        time.sleep(30)
