# Exclude a variety of commonly ignored directories.
exclude = [
    ".git",
    ".git-rewrite",
    ".pyenv",
    ".pytest_cache",
    ".ruff_cache",
    ".venv",
    ".vscode",
    "__pypackages__",
    "venv",
]

# Same as Black.
line-length = 88
indent-width = 4

# Assume Python 3.8
target-version = "py38"

[lint]
# Enable Pyflakes (`F`) and a subset of the pycodestyle (`E`)  codes by default.
# Unlike Flake8, Ruff doesn't enable pycodestyle warnings (`W`) or
# McCabe complexity (`C901`) by default.
select = ["E4", "E7", "E9", "F"]
ignore = ["E203", "E266", "E501", "F403", "F401", "W293", "F405", "F841", "E712", "E262", "E265", "W605", "C901", "E402", "F632", "F811"]


[format]
docstring-code-format = true
