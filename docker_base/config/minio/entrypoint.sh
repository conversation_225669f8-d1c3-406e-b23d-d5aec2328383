#!/bin/bash

# MinIO Entrypoint Script
#
# Responsibilities:
#   - Start MinIO server
#   - Create a default bucket: ${EXCHANGE_TYPE}-${EXECUTION_LEVEL}
#   - Enable versioning on this bucket
#   - Apply a lifecycle policy to auto-expire objects

DATA_DIR="/data"
SOURCE_ALIAS="myminio"
SOURCE_BUCKET="${EXCHANGE_TYPE}-${EXECUTION_LEVEL}"

# Start MinIO server in the background
minio server --console-address ":${MINIO_CONSOLE_PORT}" ${DATA_DIR} &

# Capture the MinIO server process ID
MINIO_PID=$!

# Function to wait for MinIO server to be ready
wait_for_minio() {
    echo "Waiting for MinIO server to be ready..."
    until mc alias set ${SOURCE_ALIAS} http://localhost:${MINIO_API_PORT} ${MINIO_ACCESS_KEY} ${MINIO_SECRET_KEY}; do
        sleep 2
    done
    echo "MinIO server is ready."
}

# Wait for MinIO server to be ready
wait_for_minio

# Check if the bucket exists
if mc ls ${SOURCE_ALIAS}/${SOURCE_BUCKET} > /dev/null 2>&1; then
    echo "Bucket ${SOURCE_BUCKET} already exists. Skipping creation."
else
    echo "Creating bucket ${SOURCE_BUCKET}..."
    # Create the bucket
    mc mb ${SOURCE_ALIAS}/${SOURCE_BUCKET} || { echo "Failed to create bucket ${SOURCE_BUCKET}"; exit 1; }
    echo "Bucket ${SOURCE_BUCKET} created."
fi

# Check and manage versioning
VERSIONING_OUTPUT=$(mc version info "${SOURCE_ALIAS}/${SOURCE_BUCKET}" 2>/dev/null)

if echo "$VERSIONING_OUTPUT" | grep -qi "is un-versioned"; then
    echo "Bucket is un-versioned. Enabling versioning..."
    mc version enable "${SOURCE_ALIAS}/${SOURCE_BUCKET}" || {
        echo "Failed to enable versioning on bucket ${SOURCE_BUCKET}"
        exit 1
    }
    echo "Versioning enabled."
elif echo "$VERSIONING_OUTPUT" | grep -qi "versioning is suspended"; then
    echo "Bucket versioning is suspended. Re-enabling..."
    mc version enable "${SOURCE_ALIAS}/${SOURCE_BUCKET}" || {
        echo "Failed to enable versioning on bucket ${SOURCE_BUCKET}"
        exit 1
    }
    echo "Versioning re-enabled."
elif echo "$VERSIONING_OUTPUT" | grep -qi "versioning is enabled"; then
    echo "Versioning is already enabled on bucket ${SOURCE_BUCKET}."
else
    echo "Unknown versioning status for bucket ${SOURCE_BUCKET}: $VERSIONING_OUTPUT"
    exit 1
fi

# Normalize JSONs to avoid cosmetic diffs
normalize_json() {
    jq --sort-keys . <<< "$1"
}

# Check and apply lifecycle policy if not present
if ! mc ilm export "${SOURCE_ALIAS}/${SOURCE_BUCKET}" > /dev/null 2>&1; then
    echo "Applying lifecycle policy..."
    mc ilm import "${SOURCE_ALIAS}/${SOURCE_BUCKET}" < /opt/lifecycle.json || { echo "Failed to apply lifecycle policy"; exit 1; }
    echo "Lifecycle policy applied."
else
    CURRENT_POLICY=$(mc ilm export "${SOURCE_ALIAS}/${SOURCE_BUCKET}" 2>/dev/null)
    # Read desired policy
    DESIRED_POLICY=$(cat /opt/lifecycle.json)
    # Compare current and desired
    if diff <(normalize_json "$CURRENT_POLICY") <(normalize_json "$DESIRED_POLICY") > /dev/null; then
        echo "Lifecycle policy is already up to date."
    else
        echo "Lifecycle policy differs. Replacing policy..."
        # Apply new policy
        mc ilm import "${SOURCE_ALIAS}/${SOURCE_BUCKET}" < /opt/lifecycle.json || { echo "Failed to apply lifecycle policy"; exit 1; }
        echo "Lifecycle policy applied."
    fi
fi

# Keep the script running if needed to keep the container alive
wait ${MINIO_PID}
