{# Global Log Processor #}
loki.process "global_log_processor" {
  forward_to = [loki.write.primary.receiver,loki.write.secondary.receiver]

  stage.drop {
    expression = "^\\s*$"
  }

  stage.multiline {
    firstline     = "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}[\\.,]\\d{3}"
    max_lines     = 0
    max_wait_time = "500ms"
  }

stage.regex {
  expression = "^(?P<timestamp>\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}[\\.,]\\d{3}) (?P<rest>.*)$"
}

stage.timestamp {
  source   = "timestamp"
  format   = "2006-01-02 15:04:05.000"
  fallback_formats = ["2006-01-02 15:04:05,000"]
  location = "Asia/Kolkata"
}

stage.output {
  source = "entry"
}
}

{# File Match and Source Macro #}
{% macro file_match_and_source(name, path, exchange, host, job, processor) %}
local.file_match "{{ name }}" {
    path_targets = [{
        __address__   = "localhost",
        __path__      = "{{ path }}",
        exchange      = "{{ exchange }}",
        host          = "{{ host }}",
        job           = "{{ job }}",
        handle_rename = true,
    }]
}

loki.source.file "{{ name }}" {
    targets    = local.file_match.{{ name }}.targets
    forward_to = [loki.process.{{ processor }}.receiver]
}
{% endmacro %}

{% set logs = [
    {
        "name": "cbe",
        "path": "/opt/logs/cbe/*.txt",
        "job": "cbe_logs_" ~ EXCHANGE_TYPE ~ "_" ~ EXECUTION_LEVEL,
        "processor": "global_log_processor"
    },
    {
        "name": "oms",
        "path": "/opt/logs/oms/*.log",
        "job": "oms_logs_" ~ EXCHANGE_TYPE ~ "_" ~ EXECUTION_LEVEL,
        "processor": "global_log_processor"
    },
    {
        "name": "oms_db_executor",
        "path": "/opt/logs/oms_db_executor/*.log",
        "job": "oms_db_exec_" ~ EXCHANGE_TYPE ~ "_" ~ EXECUTION_LEVEL,
        "processor": "global_log_processor"
    },
    {
        "name": "slave_logs",
        "path": "/opt/logs/SLAVE/*.log",
        "job": "slave_logs_" ~ EXCHANGE_TYPE ~ "_" ~ EXECUTION_LEVEL,
        "processor": "global_log_processor"
    },
    {
        "name": "cluster_logs",
        "path": "/opt/logs/CLUSTER/*.log",
        "job": "cluster_logs_" ~ EXCHANGE_TYPE ~ "_" ~ EXECUTION_LEVEL,
        "processor": "global_log_processor"
    },
    {
        "name": "watchdog_logs",
        "path": "/opt/logs/WATCHDOG/*.log",
        "job": "watchdog_logs_" ~ EXCHANGE_TYPE ~ "_" ~ EXECUTION_LEVEL,
        "processor": "global_log_processor"
    },
    {
        "name": "exit_portal",
        "path": "/opt/logs/exit_portal/*.log",
        "job": "exit_portal_" ~ EXCHANGE_TYPE ~ "_" ~ EXECUTION_LEVEL,
        "processor": "global_log_processor"
    },
    {
        "name": "exit_portal_strat",
        "path": "/opt/logs/exit_strat_log/*.log",
        "job": "exit_portal_strat_" ~ EXCHANGE_TYPE ~ "_" ~ EXECUTION_LEVEL,
        "processor": "global_log_processor"
    },
    {
        "name": "one_second_exit_application_logs",
        "path": "/opt/logs/one_second_exit_application/one_second_exit_application.log.*",
        "job": "one_second_exit_application_" ~ EXCHANGE_TYPE ~ "_" ~ EXECUTION_LEVEL,
        "processor": "global_log_processor"
    }
] %}


{# Generate Configurations for Single Exchange #}
{% for log in logs %}
{{ file_match_and_source(
    name=log.name,
    path=log.path,
    exchange=EXCHANGE_TYPE ~ "_" ~ EXECUTION_LEVEL,
    host=ALLOY_HOSTNAME,
    job=log.job,
    processor=log.processor
) }}
{% endfor %}

{# Loki Write Endpoints #}
loki.write "primary" {
    endpoint {
        url = "{{ PRIMARY_LOKI_ENDPOINT }}"
    }
    external_labels = {}
}

loki.write "secondary" {
    endpoint {
        url = "{{ SECONDARY_LOKI_ENDPOINT }}"
    }
    external_labels = {}
}