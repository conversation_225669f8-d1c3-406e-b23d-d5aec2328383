[mysqld]
pid-file        = /var/run/mysqld/mysqld.pid
socket          = /var/run/mysqld/mysqld.sock
datadir         = /var/lib/mysql
secure-file-priv= NULL
symbolic-links=0
default_authentication_plugin=mysql_native_password

bind-address            = 0.0.0.0
server-id               = {{ docker_project_id }}
log_bin                 = /var/run/mysqld/mysql-bin.log
relay-log               = /var/run/mysqld/mysql-relay-bin.log
binlog_do_db            = balte_oms

gtid_mode                = on
enforce_gtid_consistency = on
log_slave_updates        = off
binlog_cache_size = 10M
max_binlog_cache_size = 1G
net_read_timeout=600
net_write_timeout=600
log_error_verbosity = 3
