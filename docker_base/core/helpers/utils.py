import datetime
from core.helpers.configstore import (
    EXECUTION_LEVEL,
    EXCH<PERSON>GE_TYPE,
    SEGMENT,
    VAULT_URL,
    MYSQL_HOST,
    MYSQL_USER,
    MYSQL_PASSWORD,
    DB_NAME,
    BALTE_ID_STRIKE_MULTIPLIER,
)
from analytics_api import AnalyticsAPI
import asyncio
import logging
import pymysql
import pandas as pd
from typing import (
    Dict,
    List,
    Tuple,
    Union,
    Any,
    TYPE_CHECKING,
    Optional,
    NamedTuple,
    no_type_check,
)
import yaml
from yaml.loader import SafeLoader
import re
import balte.balte_config
from balte.version_compat import IS_PY311_PLUS
from balte.utility import extract_balte_id_from_mastertrade
from balte.utility_async.master_trade_type_model import MasterTradeType
from balte.enums import EntryExit
from core.helpers.one_second_exit_application_components.structs import (
    OneSecondExitPosition,
)

if TYPE_CHECKING:
    from balte.engine import EngineLive
    from balte.strategy import Strategy
    from datetime import time


def get_pyce_keys() -> Dict[str, str]:
    """read pyce keys from vault

    Raises:
        ValueError: if token is invalid and authentication fails

    Returns:
        Dict[str, str]: strategy to pyce_key mapping
    """
    import hvac  # type: ignore

    with open("/opt/balte_live/pyce_token/pyce_read.txt", "r") as f:
        token = f.read()
    client = hvac.Client(url=VAULT_URL, token=token)
    if not client.is_authenticated():
        raise ValueError("Token not valid")
    # Read a secret from the allowed path
    secret_path = (
        "python-app/pyce_keys_python311" if IS_PY311_PLUS else "python-app/pyce_keys"
    )
    if EXECUTION_LEVEL.lower() == "test":
        secret_path += "_test"
    secret = client.secrets.kv.v2.read_secret_version(path=secret_path)
    return secret["data"]["data"]  # type: ignore


def load_strats_pyce(strat_list: List[str]) -> None:
    """
    needed to be called before `from strategy import strategy` for any strategy.
    Uses `AnalyticsAPI`, replaces the old implementation that used `PYCEPathFinder`

    Args:
        strat_list (List[str]): strategies to load
    """
    pyce_branch = "test"
    if EXECUTION_LEVEL.lower() in ["production", "master"]:
        pyce_branch = "master"
    # load pyce
    analytics_api_obj = AnalyticsAPI(mode=EXECUTION_LEVEL)
    _ = analytics_api_obj.load_pyce_keys(pyce_keys=get_pyce_keys())
    _ = analytics_api_obj.load_strategy_pyce(
        strategy_name=list(strat_list), exchange=EXCHANGE_TYPE, branch=pyce_branch
    )


def filter_strategies(strat_list: List[str], filter_async: bool) -> List[str]:
    """need to call `load_strats_pyce` before it.
    Filters the list of strategies to return either
    only the async strategies or
    only the sync strategies
    according to the `filter_async` flag

    Args:
        strat_list(List[str]): List of strategies

    """
    strats = []
    for strat in strat_list:
        try:
            exec("from " + strat + " import " + strat)
        except Exception as e:
            continue
        ref_to_strat = eval(strat)
        if filter_async == asyncio.iscoroutinefunction(ref_to_strat.next):
            strats.append(strat)
    return strats


def get_live_strats() -> List[str]:
    """get live strategies from analytics api according to segment and exchange

    Returns:
        List[str]: list of live strategies
    """
    analytics_api_obj = AnalyticsAPI(mode=EXECUTION_LEVEL)
    to_be_live = analytics_api_obj.get_live_strategies(
        segment=SEGMENT.split(","), exchange=[EXCHANGE_TYPE.upper()]
    )
    return sorted(list(to_be_live))


def import_and_add_strategies(
    obj: "EngineLive", strat_list: List[str], preserve_order: bool = False
) -> None:
    """need to call `load_strats_pyce` before it.
    imports strategies, each is either added to a pickle list or a class list
    which are then added to Engine `obj`

    Args:
        obj (EngineLive): Engine to add strategies to
        strat_list (List[str]): strategies to add
        preserve_order (bool, optional): Whether to add in the same order as given. Defaults to False. (implemented in balte as class-list followed by pickle-list)
    """
    pickle_lst: List[str] = []
    class_list_name: List["Strategy"] = []
    # Import Strategies
    for strat in strat_list:
        try:
            exec("from " + strat + " import " + strat)
        except Exception as e:
            logging.exception(f"error importing strat : {repr(e)}")
            continue
        if obj.check_if_pickle_exists(strat) is not None:
            pickle_lst.append(strat)
        else:
            ref_to_strat = eval(strat)
            print("Didnt find pickle...adding as class", strat)
            class_list_name.append(ref_to_strat)

    # ADD Strategies
    if preserve_order:
        obj.add_strategy_or_pickle(
            pickle_list=pickle_lst,
            class_name_list=class_list_name,
            live_strat_order=strat_list,
            load_last_timestamp=True,
        )
    else:
        obj.add_strategy_or_pickle(
            pickle_list=pickle_lst,
            class_name_list=class_list_name,
            load_last_timestamp=True,
        )


def get_strategy_timestamps(strat_list: List[str]) -> Dict[str, List["time"]]:
    """
    Dynamically loads strategies and returns their trading timestamps.

    Strategies that fail to load or do not have a 'timestamps' attribute
    will not be included in the returned map.
    """
    timestamps_map = {}

    for strat in strat_list:
        try:
            try:
                exec("from " + strat + " import " + strat)
            except Exception as e:
                logging.exception(f"error importing strat : {repr(e)}")
                continue
            strategy_class = eval(strat)
            strategy_instance = strategy_class()

            if hasattr(strategy_instance, "timestamps"):
                timestamps_map[strat] = [
                    datetime.datetime.strptime(ts, "%H:%M").time()
                    for ts in strategy_instance.timestamps
                ]
            else:
                logging.warning(f"Strategy {strat} has no timestamps attribute")

        except Exception as e:
            logging.error(f"Failed to get timestamps of {strat}: {str(e)}")

    return timestamps_map


def read_sql_to_df(
    query: str, params: Union[List[Any], Tuple[Any], Dict[str, Any], None] = None
) -> pd.DataFrame:
    """connect to mysql db according to credentials in env vars and execute read with `query` and `params`

    Args:
        query (str): read query  to be executed

    Returns:
        pd.DataFrame: result of read query
    """
    with pymysql.connect(
        host=MYSQL_HOST,
        user=MYSQL_USER,
        password=MYSQL_PASSWORD,
        db=DB_NAME,
        charset="utf8mb4",
    ) as connection:
        return pd.read_sql(query, connection, params=params)


def get_local_timestamp() -> pd.Timestamp:
    """returns current timestamp in the timezone specified by BaLTE, but returned as a timezone-naive Timestamp object
    (i.e. consistent with that timezone but without the actual tzinfo attribute)

    Returns:
        pd.Timestamp: current timestamp
    """
    return pd.Timestamp.now(tz=balte.balte_config.TIMEZONE).replace(tzinfo=None)


def get_local_date() -> pd.Timestamp:
    """returns today's date in the timezone specified by BaLTE, but returned as a timezone-naive Timestamp object
    (i.e. consistent with that timezone but without the actual tzinfo attribute)

    Returns:
        pd.Timestamp: today's date
    """
    return get_local_timestamp().normalize()


def get_local_time() -> "time":
    """returns current time in the timezone specified by BaLTE

    Returns:
        datetime.time: today's date
    """
    return get_local_timestamp().time()


def extract_timestamp_from_filename(
    filename: str,
) -> Tuple[str, Optional[str], Optional[str]]:
    # Pattern to match date 'YYYY-MM-DD' and optional '_HH-MM'
    pattern = r"(.+?)_(\d{4}-\d{2}-\d{2})(?:_(\d{2}-\d{2}))?"

    match = re.search(pattern, filename)
    if match:
        name_part = match.group(1)  # Filename
        date_part = match.group(2)  # YYYY-MM-DD
        time_part = match.group(3) if match.group(3) else None  # HH-MM or None
        return name_part, date_part, time_part
    else:
        return filename, None, None


def load_yaml(file_path: str) -> Dict[str, Any]:
    config = {}
    with open(file_path, "r") as file:
        config = yaml.load(file, Loader=SafeLoader)
    return config


@no_type_check
def extract_balte_id(trade_info: Union[NamedTuple, OneSecondExitPosition]) -> int:
    """
    Extracts the BALTE ID from a given trade information object.

    Args:
        trade_info (Union[NamedTuple, OneSecondExitPosition]):
            A trade data object containing attributes such as symbol, strategy,
            quantity, price, segment, option type, strike price, expiry, trade ID,
            and slave name.

    Returns:
        int: The extracted BALTE ID for the trade.

    Raises:
        KeyError: If the trade symbol is not found in `balte.balte_config.symbol_to_balte_id`.
        AttributeError: If `trade_info` does not contain the required attributes.
    """
    obj = MasterTradeType(
        balte_id=balte.balte_config.symbol_to_balte_id[trade_info.SYMBOL],
        strategy=trade_info.STRATEGY,
        notional=(trade_info.QUANTITY * trade_info.ENTRY_PRICE),
        price=trade_info.ENTRY_PRICE,
        segment=trade_info.SEGMENT,
        option_type=trade_info.TYPE,
        strike=trade_info.STRIKE * BALTE_ID_STRIKE_MULTIPLIER,
        expiry_date=trade_info.EXPIRY,
        order_id=trade_info.TRADEID,
        slave_name=trade_info.SLAVE_NAME,
        entry_exit=EntryExit.ORDER_ENTRY,
    )
    return extract_balte_id_from_mastertrade(obj)
