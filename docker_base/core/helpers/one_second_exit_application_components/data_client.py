import logging
from typing import Dict, List


class OneSecondExitApplicationDataClientBase:
    """
    Handles market data retrieval for the One Second Exit Application.

    This class serves as an interface for fetching the latest traded prices of active
    positions using `balte_id`s. It is intended to be implemented with a concrete
    data-fetching mechanism, such as querying an external API, database, or market
    data feed.

    Attributes:
        one_second_exit_application_logger (logging.Logger):
            Logger instance for logging data retrieval activities.
    """

    def __init__(self, logger: logging.Logger) -> None:
        """
        Initializes an instance of OneSecondExitApplicationDataClientBase class.

        Args:
            logger (logging.Logger): Logger for tracking data-related operations.
        """
        self.one_second_exit_application_logger: logging.Logger = logger

    async def fetch_data(self, active_balte_ids: List[int]) -> Dict[int, float]:
        """
        Fetches the latest traded price data for the provided `balte_id`s.

        This method must be implemented by subclasses or concrete instances.

        Args:
            active_balte_ids (List[int]): List of `balte_id`s representing active positions.

        Returns:
            Dict[int, float]: A dictionary mapping `balte_id`s to their latest traded prices.

        Raises:
            NotImplementedError: If the method is not implemented.
        """
        raise NotImplementedError("Fetch data function is not implemented!")
