from typing import Dict, Set, List
from core.helpers.one_second_exit_application_components.structs import (
    OneSecondExitPosition,
    OrderStopLossInformation,
)
from balte.struct import TradeExtendedInfo
import logging
import pandas as pd
from core.helpers.configstore import (
    EXECUTION_LEVEL,
    EXCHANGE_TYPE,
)
import msgspec
from balte.struct import OrderStopLossInfo
from exchange.helpers.mixins import TradeQueryMixin
from sortedcontainers import SortedDict  # type: ignore


class OneSecondExitApplicationPositionManager(TradeQueryMixin):
    """
    Manages trade positions for one-second and one-minute exit strategies.

    This class is responsible for tracking trade positions that require quick exits
    based on predefined conditions. It maintains dictionaries for active positions,
    handles the assignment and removal of BALTE IDs, and ensures efficient tracking
    of trade states.

    Attributes:
        one_second_exit_positions (Dict[int, OneSecondExitPosition]):
            Stores trade positions whose stop losses are to be checked.

        active_balte_id_count_mapping (Dict[int, int]):
            Tracks the count of active BALTE IDs to monitor open positions.

        trade_ids_of_dead_trades (Set[int]):
            Maintains a set of trade IDs that are no longer active.

        one_sec_stoploss_info_list (List[OrderStopLossInformation]):
            Stores one sec stoploss information for orders.

        one_min_stoploss_info_list (List[OrderStopLossInformation]):
            Stores one min stoploss information for orders.

        one_second_exit_application_logger (logging.Logger):
            Logger to log exceptions and information during processing.

        fixed_timestamp_exit_mapping (SortedDict[pd.Timestamp, List[int]]):
            Maintains mapping of fixed timestamps exits for all timestamps.
    """

    def __init__(self, logger: logging.Logger) -> None:
        """
        Initializes an object of OneSecondExitApplicationPositionManager class  for tracking exit positions
        and active BALTE IDs.

        This setup ensures efficient tracking of positions and active trade IDs for quick access and updates.
        """
        self.one_second_exit_positions: Dict[int, OneSecondExitPosition] = {}
        self.active_balte_id_count_mapping: Dict[int, int] = {}
        self.trade_ids_of_dead_trades: Set[int] = set()
        self.one_sec_stoploss_info_list: List[OrderStopLossInformation] = []
        self.one_min_stoploss_info_list: List[OrderStopLossInformation] = []
        self.one_second_exit_application_logger: logging.Logger = logger
        self.fixed_timestamp_exit_mapping: SortedDict[pd.Timestamp, List[int]] = (
            SortedDict()
        )
        self.initialize_position_manager_from_database()

    def _update_active_balte_id_map(self, balte_id: int, counter: int) -> None:
        """
        Updates the mapping of active BALTE IDs by incrementing or decrementing their count.

        This function updates the `active_balte_id_count_mapping` dictionary by adjusting the
        count associated with a given `balte_id`. If the `balte_id` is not already present,
        it initializes the count to zero before applying the `counter` update. If the resulting
        count is zero or negative, the `balte_id` is removed from the mapping.

        Args:
            balte_id (int): The unique identifier of the BALTE trade.
            counter (int): The value to add or subtract from the BALTE ID's count.
        """
        if balte_id not in self.active_balte_id_count_mapping:
            self.active_balte_id_count_mapping[balte_id] = 0
        self.active_balte_id_count_mapping[balte_id] += counter
        if self.active_balte_id_count_mapping[balte_id] <= 0:
            self.active_balte_id_count_mapping.pop(balte_id, None)

    def _add_fixed_timestamp_exit(
        self, exit_timestamp: pd.Timestamp, trade_id: int
    ) -> None:
        """
        Add a trade ID to the list of trades scheduled to exit at a specific timestamp.

        If the timestamp is not already present in the mapping, it initializes the list.
        Appends the given trade ID to the list corresponding to the provided timestamp.

        Args:
            exit_timestamp (pd.Timestamp): The timestamp at which the trade should be exited.
            trade_id (int): The ID of the trade to be scheduled for exit.
        """
        if exit_timestamp not in self.fixed_timestamp_exit_mapping:
            self.fixed_timestamp_exit_mapping[exit_timestamp] = []
        self.fixed_timestamp_exit_mapping[exit_timestamp].append(trade_id)

    def _update_stoploss_informations(
        self, trade_id: int, order_stoploss_info_list: List[OrderStopLossInfo]
    ) -> None:
        """
        Updates stop-loss information by categorizing orders into fixed timestamp exits, one-second and one-minute exit lists.

        This method processes a list of `OrderStopLossInfo` objects, updates the active `balte_id`
        map with a counter, and segregates stop-loss orders into three categories:

        - `fixed_timestamp_exit_mapping`: Contains orders that are to be exited at a fixed timestamp.
        - `one_sec_stoploss_info_list`: Contains orders that do **not** exit on 1-minute intervals.
        - `one_min_stoploss_info_list`: Contains orders that **do** exit on 1-minute intervals.

        The categorized stop-loss information is then appended to the respective class-level lists.

        Args:
            trade_id (int): The trade ID associated with the stop-loss orders.
            order_stoploss_info_list (List[OrderStopLossInfo]): A list of stop-loss order information.
        """
        one_sec_stoploss_list: List[OrderStopLossInfo] = []
        one_min_stoploss_list: List[OrderStopLossInfo] = []
        for order_stoploss_info in order_stoploss_info_list:
            if order_stoploss_info.fixed_exit_timestamp:
                self._add_fixed_timestamp_exit(
                    exit_timestamp=order_stoploss_info.fixed_exit_timestamp,
                    trade_id=trade_id,
                )
                continue
            self._update_active_balte_id_map(
                balte_id=order_stoploss_info.balte_id, counter=1
            )
            if order_stoploss_info.exit_on_1min is False:
                one_sec_stoploss_list.append(order_stoploss_info)
            else:
                one_min_stoploss_list.append(order_stoploss_info)
        self.one_sec_stoploss_info_list.extend(
            OrderStopLossInformation.initialize_list_from_stoploss_info(
                trade_id=trade_id, order_stoploss_info_list=one_sec_stoploss_list
            )
        )
        self.one_min_stoploss_info_list.extend(
            OrderStopLossInformation.initialize_list_from_stoploss_info(
                trade_id=trade_id, order_stoploss_info_list=one_min_stoploss_list
            )
        )

    def add_position(self, one_second_exit_position: OneSecondExitPosition) -> None:
        """
        Adds a trade position to the appropriate exit tracking dictionary.

        This function processes a `OneSecondExitPosition` instance and adds it to the
        `one_second_exit_positions` dictionary and also adds the stop-loss exit conditions.
        If the trade lacks stop-loss information or has already been marked as a "dead trade,"
        it is ignored. The function also updates the active BALTE ID mapping based on stoploss conditions.

        Args:
            one_second_exit_position (OneSecondExitPosition): The trade position to be added.
        """
        extended_order_info: TradeExtendedInfo = msgspec.json.decode(
            one_second_exit_position.EXTENDED_INFO.encode("ASCII"),
            type=TradeExtendedInfo,
        )
        if (extended_order_info.order_stoploss_info is None) or (
            one_second_exit_position.TRADEID in self.trade_ids_of_dead_trades
        ):
            return
        self.one_second_exit_positions[one_second_exit_position.TRADEID] = (
            one_second_exit_position
        )
        self._update_stoploss_informations(
            trade_id=one_second_exit_position.TRADEID,
            order_stoploss_info_list=extended_order_info.order_stoploss_info,
        )

    def remove_position(self, trade_id: int, balte_id: int = -1) -> None:
        """
        Removes a trade position from the tracking dictionaries and updates related mappings.

        This function removes the trade position associated with the given `trade_id` from both
        `one_minute_exit_positions` and `one_second_exit_positions`, if present. It also marks
        the trade as "dead" by adding the `trade_id` to `trade_ids_of_dead_trades`. Additionally,
        it updates the active BALTE ID mapping by decrementing its counter.

        Args:
            trade_id (int): The unique identifier of the trade to be removed.
            balte_id (int): The BaLTE ID of the stoploss condition being removed. Defaults to -1.
        """
        self.one_second_exit_positions.pop(trade_id, None)
        self.trade_ids_of_dead_trades.add(trade_id)
        self._update_active_balte_id_map(balte_id=balte_id, counter=-1)

    def _load_live_positions(self) -> None:
        """
        Loads live trading positions from the database.

        This method retrieves active trade positions from the live trading table,
        initializes them as `OneSecondExitPosition` objects, and adds them to
        the position manager for tracking.

        Raises:
            Exception: If there is an error while fetching or processing database records.
        """
        live_table: str = f"{EXCHANGE_TYPE}_{EXECUTION_LEVEL}"
        self.one_second_exit_application_logger.info(
            f"Loading DB positions from {live_table}"
        )
        try:
            records: pd.DataFrame = self.get_all_live_trades()
            for _, trade_info in records.iterrows():
                one_second_exit_position: OneSecondExitPosition = (
                    OneSecondExitPosition.initialize_from_database_entry(
                        trade_info=trade_info
                    )
                )
                self.add_position(one_second_exit_position=one_second_exit_position)
        except Exception as e:
            self.one_second_exit_application_logger.error(
                f"Error loading live positions from database due to {repr(e)}"
            )
            raise

    def _remove_dead_positions(self) -> None:
        """
        Removes inactive (dead) trade positions from the active trade list.

        This method fetches trade IDs of closed or invalidated positions from
        the dead trades database table and removes them from the position manager.

        Raises:
            Exception: If an error occurs while accessing the database or processing records.
        """
        dead_table: str = f"dead_trade_{EXCHANGE_TYPE}_{EXECUTION_LEVEL}"
        self.one_second_exit_application_logger.info(
            f"Loading dead positions from {dead_table}"
        )
        try:
            dead_trades: pd.DataFrame = self.get_all_dead_trades()
            for trade_id in dead_trades["TRADEID"]:
                self.remove_position(trade_id=trade_id)
        except Exception as e:
            self.one_second_exit_application_logger.error(
                f"Error loading dead positions from database due to {repr(e)}"
            )
            raise

    def initialize_position_manager_from_database(self) -> None:
        """
        Initializes the position manager by loading active and dead trade positions from the database.

        This method:
        - Loads live positions from the active trades table.
        - Removes positions that are no longer valid (dead trades).
        - Initializes the active Balte ID mapping for trade tracking.

        Raises:
            Exception: If there is an issue accessing or processing the database records.
        """
        self._load_live_positions()
        self._remove_dead_positions()

        self.one_second_exit_application_logger.info(
            f"Position Manager Initialized: {len(self.one_second_exit_positions)} total positions loaded "
            f"({len(self.one_sec_stoploss_info_list)} one-second stoploss conditions loaded, "
            f"{len(self.one_min_stoploss_info_list)} one-minute stoploss conditions loaded), "
            f"{sum(len(self.fixed_timestamp_exit_mapping[fixed_timestamp_exits]) for fixed_timestamp_exits in self.fixed_timestamp_exit_mapping)} fixed timestamp exits loaded."
        )
