from dataclasses import dataclass
import pandas as pd
from toti.rpc import oms_pb2
from balte.struct import OrderStopLossInfo
from typing import List


@dataclass
class OneSecondExitPosition:
    """
    Represents a trade position in the One Second Exit application.

    This class encapsulates details of a trade, including its trade ID, symbol, quantity,
    strategy, entry price, timestamps, and additional trade metadata.

    Methods:
        initialize_from_database_entry(trade_info: pd.Series) -> OneSecondExitPosition:
            Creates an instance of `OneSecondExitPosition` from a database entry.

        initialize_from_kafka_message(kafka_trade_message: oms_pb2.LogMessage) -> OneSecondExitPosition:
            Creates an instance of `OneSecondExitPosition` from a Kafka trade message.
    """

    TRADEID: int  # Unique identifier for the trade.
    SYMBOL: str  # Trading symbol of the security.
    QUANTITY: int  # Number of units in the trade.
    STRATEGY: str  # Name of the trading strategy used.
    ENTRY_TIMESTAMP: pd.Timestamp  # Timestamp when the trade was placed.
    SEGMENT: str  # Market segment (e.g., OPTIDX, FUTSTK, etc).
    EXPIRY: str  # Expiry date for derivatives (if applicable).
    TYPE: str  # Trade type (e.g., CE, PE, NONE).
    STRIKE: float  # Strike price for options (if applicable).
    ENTRY_PRICE: float  # Price at which the trade was placed.
    ORDER_TYPE: str  # Type of order (e.g., NORMAL, HEDGE).
    ENTRY_TIMESTAMP_META: pd.Timestamp  # Metadata timestamp for trade entry.
    SLAVE_NAME: str  # Name of the slave node placing the trade.
    EXTENDED_INFO: str  # Additional trade-related metadata.

    @classmethod
    def initialize_from_database_entry(
        cls,
        trade_info: pd.Series,  # type: ignore
    ) -> "OneSecondExitPosition":
        """
        Creates an instance of `OneSecondExitPosition` from a database record.

        This method extracts trade information stored as a Pandas Series, decodes the
        extended order info from JSON, and initializes a `OneSecondExitPosition` object.

        Args:
            trade_info (pd.Series): A Pandas Series containing trade details.

        Returns:
            OneSecondExitPosition: A new instance populated with the trade data.
        """
        return cls(**trade_info.to_dict())

    @classmethod
    def initialize_from_kafka_message(
        cls, kafka_trade_message: oms_pb2.LogMessage
    ) -> "OneSecondExitPosition":
        """
        Creates an instance of `OneSecondExitPosition` from a Kafka trade message.

        This method decodes the extended order info from the Kafka message and initializes
        a `OneSecondExitPosition` object with relevant trade attributes.

        Args:
            kafka_trade_message (oms_pb2.LogMessage): Kafka message containing trade data.

        Returns:
            OneSecondExitPosition: A new instance populated with the trade data.
        """
        return cls(
            TRADEID=kafka_trade_message.trade_id,
            SYMBOL=kafka_trade_message.symbol,
            QUANTITY=kafka_trade_message.quantity,
            STRATEGY=kafka_trade_message.strategy,
            ENTRY_TIMESTAMP=pd.Timestamp(kafka_trade_message.entry_timestamp),
            SEGMENT=kafka_trade_message.segment,
            EXPIRY=kafka_trade_message.expiry,
            TYPE=kafka_trade_message.type,
            STRIKE=kafka_trade_message.strike,
            ENTRY_PRICE=kafka_trade_message.entry_price,
            ORDER_TYPE=kafka_trade_message.order_type,
            ENTRY_TIMESTAMP_META=pd.Timestamp(kafka_trade_message.entry_timestamp_meta),
            SLAVE_NAME=kafka_trade_message.slave_name,
            EXTENDED_INFO=kafka_trade_message.extended_info,
        )


@dataclass
class OrderStopLossInformation:
    """
    Represents an order stoploss information object.

    This class encapsulates details of a stoploss information, including its trade ID, universe, balte_id,
    price, operator, and flag to indicate whether we want to exit on 1 min or not.

    Methods:
        initialize_list_from_stoploss_info(order_stoploss_info_list: List[OrderStopLossInfo]) -> List[OrderStopLossInformation]:
            Creates an instance of `OrderStopLossInformation` from an array of stoploss lists.

    """

    trade_id: int  # Trade ID of the entry trade.
    order_stoploss_info: OrderStopLossInfo  # Stoploss Information metadata.

    @classmethod
    def initialize_list_from_stoploss_info(
        cls, trade_id: int, order_stoploss_info_list: List[OrderStopLossInfo]
    ) -> List["OrderStopLossInformation"]:
        """
        Creates a list of OrderStopLossInformation instances from a list of OrderStopLossInfo.

        Args:
            trade_id (int): The trade ID associated with the stop-loss orders.
            order_stoploss_info_list (List[OrderStopLossInfo]): List of stop-loss info objects.

        Returns:
            List[OrderStopLossInformation]: A list of OrderStopLossInformation instances.
        """
        return [
            cls(trade_id=trade_id, order_stoploss_info=info)
            for info in order_stoploss_info_list
        ]
