from email.mime.multipart import MIMEMultipart
import smtplib

from email.mime.text import MIMEText
import requests
import traceback
import os

from core.helpers.configstore import WEBHOOK

from typing import Dict, Any, List
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from pendulum import DateTime
    from airflow.models.taskinstance import TaskInstance


def task_fail_alert(context: Dict[str, Any]) -> None:
    """
    Sends an alert to Google Chat in case of task failure.
    Args:
        context (dict): Context object containing information about the task instance.
    """
    print("task_fail_alert()")
    exception: BaseException = context["exception"]
    task_instance: "TaskInstance" = context["task_instance"]
    logical_date: "DateTime" = context["logical_date"]
    formatted_exception = str(exception)
    try:
        tb = None if isinstance(exception, str) else exception.__traceback__
        formatted_exception = (
            "".join(traceback.format_exception(type(exception), value=exception, tb=tb))
            + "log_url: "
            + task_instance.log_url.replace(
                "localhost:8080",
                f"{os.environ.get('HOST_IP')}:{os.environ.get('AIRFLOW_EXPOSED_PORT')}",
            )
            + " please check logs."
        )
    except Exception as err:
        print(f"Unexpected {err=}, {type(err)=} while sending task fail alert")
        pass

    # form a card to represent alert in a better way.
    body = {
        "cardsV2": [
            {
                "cardId": "createCardMessage",
                "card": {
                    "header": {
                        "title": "{task} is failed after {tries} tries".format(
                            task=task_instance.task_id,
                            tries=task_instance.prev_attempted_tries,
                        ),
                        "subtitle": task_instance.dag_id,
                        "imageUrl": "https://img.icons8.com/fluency/48/delete-sign.png",
                    },
                    "sections": [
                        {
                            "widgets": [
                                {
                                    "textParagraph": {
                                        "text": f"<b>Execution Time:</b> <time>{logical_date}</time>",
                                    }
                                },
                                {
                                    "textParagraph": {
                                        "text": f"<b>Task Duration: </b> {task_instance.duration}s",
                                    }
                                },
                                {
                                    "textParagraph": {
                                        "text": f"<b>Exception:</b> <i>{str(exception)[:150]}</i>",
                                    }
                                },
                            ]
                        }
                    ],
                },
            }
        ]
    }

    full_url = WEBHOOK
    print("sending alert card")
    _make_http_request(body, full_url)

    print("sending exception as a thread")
    exception_message = {
        "text": f"""<users/all>
                    ```{formatted_exception}```"""
    }
    _make_http_request(exception_message, full_url)


def _make_http_request(body: Any, full_url: str) -> None:
    """
    Sends an HTTP POST request with the provided body to the given URL.
    Args:
        body (dict): The request body.
        full_url (str): The URL to send the request to.
    """
    r = requests.post(
        url=full_url,
        json=body,
        headers={"Content-type": "application/json"},
    )
    print(r.status_code, r.ok)


def send_message_to_gspace(msg: str, url: str) -> None:
    """_summary_

    Args:
        msg (str): _description_
        url (str): _description_
    """
    app_message = {"text": msg}
    message_headers = {"Content-Type": "application/json; charset=UTF-8"}
    requests.post(
        url=url,
        headers=message_headers,
        json=app_message,
    )


def send_message_to_teams(text: str, url: str) -> None:
    """_summary_

    Args:
        text (str): _description_
        url (str): _description_
    """
    import json
    from urllib import request

    post = {
        "@context": "https://schema.org/extensions",
        "@type": "MessageCard",
        "themeColor": "0072C6",
        "title": "",
        "text": text,
    }
    try:
        json_data = json.dumps(post)
        req = request.Request(
            url,
            data=json_data.encode("ascii"),
            headers={"Content-Type": "application/json"},
        )
        resp = request.urlopen(req)
    except Exception as em:
        print("EXCEPTION: " + str(em))


def send_email(sub: str, txt: str, recipients: List[str], msgtype: str) -> None:
    fromaddr = "<EMAIL>"
    toaddr = ", ".join(recipients)
    msg = MIMEMultipart()
    msg["From"] = fromaddr
    msg["To"] = toaddr
    msg["Subject"] = sub
    msg.attach(MIMEText(txt, msgtype))
    s = smtplib.SMTP("smtp.gmail.com", 587)
    s.starttls()
    s.login(fromaddr, "gtfsmppaxwezdobh")
    text = msg.as_string()
    s.sendmail(fromaddr, recipients, text)
    s.quit()
