import os
import datetime
from core.helpers.configstore import EXECUTION_LEVEL, WEBHOOK
from core.helpers.alerting import send_message_to_gspace
from typing import Any
from core.helpers.utils import get_local_timestamp
from core.runners.runner_base import RunnerBase


class OMSLatencyCheckRunnerBase(RunnerBase):
    def __init__(self) -> None:
        super().__init__()
        self.oms_mode = "test"
        if EXECUTION_LEVEL.lower() in ["production", "master"]:
            self.oms_mode = "production"
        self.NON_BALTE_ORDERS_MAX_THRESHOLD: int = 5
        self.BALTE_ORDERS_MAX_THRESHOLD: int = 2
        self.today_date = get_local_timestamp().strftime("%Y-%m-%d")
        self.LOG_PATH = "/opt/balte_live/log/oms/"

    def handle_non_balte_orders(self, log_file_path: str) -> None:
        with open(log_file_path, "r") as file:
            log_content = file.read()

        lines = log_content.strip().split("\n")
        lines = [
            line
            for line in lines
            if ((self.today_date in line) and ("BaLTE_" not in line))
        ]

        generated_lines = [
            line for line in lines if "Generated Real Order String" in line
        ]
        sent_lines = [line for line in lines if "Sent Real Order String" in line]
        time_diff_list = []

        for generated_line, sent_line in zip(generated_lines, sent_lines):
            generated_time = datetime.datetime.strptime(
                generated_line.split(" - ")[0], "%Y-%m-%d %H:%M:%S,%f"
            )
            sent_time = datetime.datetime.strptime(
                sent_line.split(" - ")[0], "%Y-%m-%d %H:%M:%S,%f"
            )
            time_diff = (sent_time - generated_time).total_seconds() * 1000
            time_diff_list.append(time_diff)

        if time_diff_list:
            average_time_diff = sum(time_diff_list) / len(time_diff_list)
            if average_time_diff >= self.NON_BALTE_ORDERS_MAX_THRESHOLD:
                send_message_to_gspace(
                    f"Non balte orders latency in oms: {average_time_diff:.3f} milliseconds breached {self.NON_BALTE_ORDERS_MAX_THRESHOLD} milliseconds",
                    WEBHOOK,
                )
        else:
            send_message_to_gspace(
                "PLEASE CHECK: No entries found in the oms signal log file.", WEBHOOK
            )

    def handle_balte_orders(self, log_file_path: str) -> None:
        with open(log_file_path, "r") as file:
            log_content = file.read()

        lines = log_content.strip().split("\n")
        lines = [
            line for line in lines if ((self.today_date in line) and ("BaLTE_" in line))
        ]
        received_lines = [line for line in lines if "Received BaLTE" in line]
        generated_lines = [
            line for line in lines if "Generated Real Order String" in line
        ]

        generated_dict = {}

        # Populate generated_dict based on trade_id
        for generated_line in generated_lines:
            generated_trade_id = int(generated_line.split(",")[8].strip())
            if "10010" in generated_line:
                generated_trade_id = int(str(generated_trade_id))
            else:
                generated_trade_id = int(str(generated_trade_id))
            generated_dict[generated_trade_id] = generated_line

        time_diff_list = []

        # Iterate over received lines
        for received_line in received_lines:
            received_trade_id = int(received_line.split(":")[3].split(",")[0].strip())

            if "Entry" in received_line:
                received_trade_id = int("10011" + str(received_trade_id))
            else:
                received_trade_id = int("10010" + str(received_trade_id))

            # Look up the generated line based on trade_id
            generated_line = generated_dict.get(received_trade_id, "")

            if generated_line:
                generated_time = datetime.datetime.strptime(
                    generated_line.split(" - ")[0], "%Y-%m-%d %H:%M:%S,%f"
                )
                received_time = datetime.datetime.strptime(
                    received_line.split(" - ")[0], "%Y-%m-%d %H:%M:%S,%f"
                )
                time_diff = (generated_time - received_time).total_seconds() * 1000
                if time_diff > 0:
                    time_diff_list.append(time_diff)
            else:
                print(
                    f"No match found for trade_id {received_trade_id} in generated lines."
                )

        if time_diff_list:
            average_time_diff = sum(time_diff_list) / len(time_diff_list)
            if average_time_diff >= self.BALTE_ORDERS_MAX_THRESHOLD:
                send_message_to_gspace(
                    f"Balte orders latency in oms: {average_time_diff:.3f} milliseconds breached {self.BALTE_ORDERS_MAX_THRESHOLD} milliseconds",
                    WEBHOOK,
                )
        else:
            send_message_to_gspace(
                "PLEASE CHECK: No entries found in the oms mappings log file.", WEBHOOK
            )

    def run(self, *args: Any, **kwargs: Any) -> None:
        signal_log_file_path = os.path.join(
            self.LOG_PATH, f"oms_{self.oms_mode}_signal.log"
        )
        self.handle_non_balte_orders(signal_log_file_path)

        mapping_log_file_path = os.path.join(
            self.LOG_PATH, f"oms_{self.oms_mode}_mapping.log"
        )
        self.handle_balte_orders(mapping_log_file_path)
