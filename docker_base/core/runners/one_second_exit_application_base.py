import os
import datetime
from core.helpers.configstore import (
    OMS_HOST,
    OMS_PORT,
    EXCHANGE_TYPE,
    TRADING_START_HOUR,
    TRADING_START_MINUTE,
    EXCHANGE_TYPE_MAPPING,
)
from core.helpers.utils import get_local_timestamp
from core.runners.runner_balte import RunnerBaLTE
from exchange.helpers.mixins import OmsOpsMixin
import logging
import pandas as pd
import toti
import asyncio
from typing import Dict, List, Any, Optional
from balte.enums import ExchangeType
import balte.balte_config
import msgspec
from core.helpers.one_second_exit_application_components.kafka_trade_processor import (
    KafkaTradeProcessor,
)
from core.helpers.one_second_exit_application_components.position_manager import (
    OneSecondExitApplicationPositionManager,
)
from exchange.helpers.one_second_exit_application_components.data_client import (
    OneSecondExitApplicationDataClient,
)
from core.helpers.one_second_exit_application_components.structs import (
    OneSecondExitPosition,
    OrderStopLossInformation,
)


class OMSOneSecondExitApplicationRunnerBase(RunnerBaLTE, OmsOpsMixin):
    """
    Base class for running the One Second Exit Application in an OMS environment.

    This class manages the execution of a high-frequency exit strategy by:
    - Handling market data updates and evaluating stop-loss conditions.
    - Consuming trade-related messages from Kafka to update active positions.
    - Managing trade exits using `Toti` for OMS-based order execution.
    - Logging application activity for monitoring and debugging.

    Key Features:
    - Initializes the OMS production environment and exchange connection.
    - Maintains active trade positions through `OneSecondExitApplicationPositionManager`.
    - Processes incoming trade messages using `KafkaTradeProcessor`.
    - Retrieves market data via `OneSecondExitApplicationDataClient`.
    - Implements a periodic task to evaluate stop-loss and execute exits.
    - Supports both one-second and one-minute exit strategies.

    Attributes:
        toti_obj (toti.Toti):
            Instance of `Toti` to interact with the exchange and manage orders.
        next_onemin_timestamp (pd.Timestamp):
            Timestamp of the next one-minute interval for synchronization.
        is_market_closed (asyncio.Future):
            Flag to indicate if market is closed. Defaults to False.
        position_manager (OneSecondExitApplicationPositionManager):
            Manages active trade positions and their lifecycle.
        kafka_trade_processor (KafkaTradeProcessor):
            Handles consuming and processing trade messages from Kafka.
        data_client (OneSecondExitApplicationDataClient):
            Fetches market data for evaluating stop-loss conditions.
        one_second_exit_application_logger (logging.Logger):
            Logger instance for recording application activity.
        market_closing_time (pd.Timestamp):
            Stores the market closing time for the exchange.
    """

    def __init__(self) -> None:
        """
        Initializes an instance of the OMSOneSecondExitApplicationRunnerBase class.

        This constructor sets up the application components, including:
        - Configuring Toti in production mode with the OMS server.
        - Initializing a `toti.Toti` object for handling exchange operations.
        - Initializing the next one-minute timestamp for synchronization with execution intervals.
        - Configuring a logger for application-wide logging.
        - Initializing the position manager for tracking active trade positions.
        - Setting up a Kafka trade processor for consuming and processing trade messages.
        - Initializing a data client for fetching market-related data.
        """
        super().__init__()
        toti.Toti.set_production_mode(f"{OMS_HOST}:{OMS_PORT}")
        exchange_type_enum: ExchangeType = ExchangeType(
            EXCHANGE_TYPE_MAPPING[EXCHANGE_TYPE.lower()]
        )
        self.toti_obj = toti.Toti(exchange_type_enum)
        self.next_onemin_timestamp: pd.Timestamp = max(
            get_local_timestamp()
            .normalize()
            .replace(hour=TRADING_START_HOUR, minute=TRADING_START_MINUTE),
            get_local_timestamp().ceil("min"),
        )
        self.is_market_closed: asyncio.Future = asyncio.Future()  # type: ignore
        self.one_second_exit_application_logger: logging.Logger = self.setup_logger()
        self.position_manager: OneSecondExitApplicationPositionManager = (
            OneSecondExitApplicationPositionManager(
                logger=self.one_second_exit_application_logger
            )
        )
        self.kafka_trade_processor: KafkaTradeProcessor = KafkaTradeProcessor(
            position_manager=self.position_manager,
            logger=self.one_second_exit_application_logger,
        )
        self.data_client: OneSecondExitApplicationDataClient = (
            OneSecondExitApplicationDataClient(
                logger=self.one_second_exit_application_logger
            )
        )
        self.market_closing_time: pd.Timestamp = (
            get_local_timestamp()
            .normalize()
            .replace(
                hour=balte.balte_config.MARKET_CLOSE_HOUR,
                minute=balte.balte_config.MARKET_CLOSE_MINUTE,
            )
        )
        self.one_second_exit_application_logger.info(
            "One Second Exit Application is LIVE"
        )

    def setup_logger(self) -> logging.Logger:
        """
        Sets up and configures the application logger.

        This method initializes a logger to capture application logs and writes them to a
        specified log file. It sets the logging level to INFO and adds a file handler to
        store logs in the `/opt/balte_live/log/one_second_exit_application` directory.

        Returns:
            logging.Logger: Configured logger instance for the application.
        """
        LOG_DIR = "/opt/balte_live/log/one_second_exit_application"
        LOG_FILE = os.path.join(
            LOG_DIR,
            f"one_second_exit_application.log.{get_local_timestamp().normalize().strftime('%Y-%m-%d')}",
        )
        logger: logging.Logger = logging.getLogger()
        logger.setLevel(logging.INFO)
        fh = logging.FileHandler(LOG_FILE)
        fh.setLevel(logging.INFO)
        formatter = logging.Formatter(
            "%(asctime)s.%(msecs)03d %(levelname)s %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
        )
        fh.setFormatter(formatter)
        logger.addHandler(fh)
        return logger

    def _evaluate_stop_loss_condition(
        self, curr_price: float, stoploss_price: float, operator: str
    ) -> bool:
        """
        Check if the stop-loss condition is met based on the given operator.

        Args:
            curr_price (float): The current price.
            stoploss_price (float): The stop-loss price.
            operator (str): Comparison operator ('<', '>', '<=', '>=').

        Returns:
            bool: True if the stop-loss condition is met, otherwise False.

        Raises:
            ValueError: If an invalid operator is provided.
        """
        comparison_functions = {
            ">": lambda x, y: x > y,
            "<": lambda x, y: x < y,
            ">=": lambda x, y: x >= y,
            "<=": lambda x, y: x <= y,
        }

        compare_function = comparison_functions.get(operator, None)
        if not callable(compare_function):
            raise ValueError(
                f"Invalid operator: {operator}. Expected one of {list(comparison_functions.keys())}."
            )

        return compare_function(curr_price, stoploss_price)  # type: ignore

    def _get_nearest_5_min_bar(self) -> pd.Timestamp:
        """Function to get nearest 5 min bar.

        Returns:
            pd.Timestamp: nearest 5 min bar.
        """
        current_time = get_local_timestamp()
        rounded_time = current_time + (
            datetime.timedelta(minutes=5)
            - datetime.timedelta(minutes=current_time.minute % 5)
        )
        rounded_time = rounded_time.replace(second=0)
        return rounded_time

    def send_exit_trade_and_remove_stoploss_information(
        self, trade_id: int, balte_id: int
    ) -> None:
        """
        Sends an exit trade for the given trade ID and removes its associated stoploss information.

        This method retrieves the position associated with the given trade ID, logs the stoploss hit,
        sends an exit trade using the appropriate metadata and trading object, then removes the position
        from the position manager. It also logs successful trade exit.

        Args:
            trade_id (int): The ID of the trade to be exited.
            balte_id (int): The associated Balte ID for the trade.
        """
        position: OneSecondExitPosition = (
            self.position_manager.one_second_exit_positions[trade_id]
        )
        self.one_second_exit_application_logger.info(
            f"Stoploss hit for order {position}"
        )
        exit_timestamp_meta: pd.Timestamp = self._get_nearest_5_min_bar()
        if exit_timestamp_meta == self.market_closing_time:
            self.one_second_exit_application_logger.warning(
                f"Skipping sending exit for order with tradeID : {position.TRADEID} as exit sending time exceeds threshold."
            )
            return
        self.send_exit_trade(
            trade_info=position,
            exit_timestamp_meta=self._get_nearest_5_min_bar(),
            toti_obj=self.toti_obj,
        )
        self.position_manager.remove_position(
            trade_id=position.TRADEID, balte_id=balte_id
        )
        self.one_second_exit_application_logger.info(
            f"Order {position.TRADEID} exited successfully."
        )

    def check_exit_conditions_and_send_exit_if_stoploss_hit(
        self,
        stoploss_informations: List[OrderStopLossInformation],
        last_traded_price_data: Dict[int, float],
    ) -> List[OrderStopLossInformation]:
        """
        Checks whether any open positions meet exit conditions based on stop-loss rules.

        This method iterates through the provided stoploss informations and evaluates whether
        the stop-loss condition is met for each position. If a position's stop-loss
        price is breached, an exit trade is triggered, and the position is removed
        from the position manager.

        Args:
            stoploss_informations List[OrderStopLossInformation]:
                A list of stoploss informations to be checked.
            last_traded_price_data (Dict[int, float]):
                A dictionary mapping `balte_id`s to the latest traded prices.

        Returns:
            List[OrderStopLossInformation]:
                A list of active stoploss conditions after the iteration.

        Behavior:
            - Evaluates stop-loss conditions using `_evaluate_stop_loss_condition`.
            - If the stop-loss condition is met:
                - Logs the stop-loss trigger.
                - Sends an exit trade using `send_exit_trade`.
                - Removes the exited position from the position manager.
        """
        activate_stoploss_conditions: List[OrderStopLossInformation] = []
        for stoploss_info in stoploss_informations:
            balte_id: int = stoploss_info.order_stoploss_info.balte_id
            if (
                stoploss_info.trade_id in self.position_manager.trade_ids_of_dead_trades
                or stoploss_info.trade_id
                not in self.position_manager.one_second_exit_positions
            ):
                self.position_manager.remove_position(
                    trade_id=stoploss_info.trade_id, balte_id=balte_id
                )
                continue
            last_traded_price: Optional[float] = last_traded_price_data.get(balte_id)
            if last_traded_price and self._evaluate_stop_loss_condition(
                curr_price=last_traded_price,
                stoploss_price=stoploss_info.order_stoploss_info.price,
                operator=stoploss_info.order_stoploss_info.operator,
            ):
                self.send_exit_trade_and_remove_stoploss_information(
                    trade_id=stoploss_info.trade_id, balte_id=balte_id
                )
            else:
                activate_stoploss_conditions.append(stoploss_info)
        return activate_stoploss_conditions

    def check_and_send_fixed_timestamp_exits(self) -> None:
        """
        Checks for and processes all fixed timestamp-based exits whose scheduled time has arrived.

        This method compares the current local timestamp with the earliest scheduled exit timestamp
        in the position manager. If the current time is equal to or later than the scheduled time,
        it sends exit trades for all associated trade IDs and removes the timestamp entry from the mapping.

        The method continues processing until there are no due exits remaining or no scheduled exits left.
        """
        current_timestamp: pd.Timestamp = get_local_timestamp()
        while len(self.position_manager.fixed_timestamp_exit_mapping):
            nearest_timestamp: pd.Timestamp = (
                self.position_manager.fixed_timestamp_exit_mapping.iloc[0]
            )
            if current_timestamp < nearest_timestamp:
                break
            exits_to_be_sent: List[int] = (
                self.position_manager.fixed_timestamp_exit_mapping.pop(
                    nearest_timestamp
                )
            )
            for trade_id in exits_to_be_sent:
                if trade_id in self.position_manager.trade_ids_of_dead_trades:
                    continue
                self.send_exit_trade_and_remove_stoploss_information(
                    trade_id=trade_id, balte_id=-1
                )

    async def fetch_data_and_check_exit_conditions(self) -> None:
        """
        Continuously fetches market data and checks for exit conditions.

        This method runs in an infinite loop, periodically fetching the latest
        traded price data for active positions and evaluating whether any
        stop-loss or exit conditions are met.

        Behavior:
            - Retrieves active `balte_id`s from `position_manager`.
            - Fetches the latest traded price data from `data_client`.
            - Checks exit conditions for `one_second_stoploss_conditions`.
            - If the current time surpasses `next_onemin_timestamp`:
                - It also checks `fixed_timestamp_exits_conditions`.
                - It checks exit conditions for `one_minute_stoploss_conditions`.
                - Updates the next one-minute timestamp.
            - Logs any unexpected errors encountered during execution.
            - Waits for 10 milliseconds before the next iteration.
        """
        while not self.is_market_closed.done():
            try:
                active_balte_ids: List[int] = list(
                    self.position_manager.active_balte_id_count_mapping.keys()
                )
                last_traded_price_data: Dict[
                    int, float
                ] = await self.data_client.fetch_data(active_balte_ids=active_balte_ids)
                self.position_manager.one_sec_stoploss_info_list = self.check_exit_conditions_and_send_exit_if_stoploss_hit(
                    stoploss_informations=self.position_manager.one_sec_stoploss_info_list,
                    last_traded_price_data=last_traded_price_data,
                )
                if get_local_timestamp() > self.next_onemin_timestamp:
                    self.check_and_send_fixed_timestamp_exits()
                    self.position_manager.one_min_stoploss_info_list = self.check_exit_conditions_and_send_exit_if_stoploss_hit(
                        stoploss_informations=self.position_manager.one_min_stoploss_info_list,
                        last_traded_price_data=last_traded_price_data,
                    )
                    self.next_onemin_timestamp = get_local_timestamp().ceil("min")
            except Exception as e:
                self.one_second_exit_application_logger.error(
                    f"Unexpected error occured - {repr(e)}"
                )
            await asyncio.sleep(0.01)

    async def consume_kafka_messages_and_update_positions(self) -> None:
        """
        Consumes messages from Kafka and updates trade positions accordingly.

        This method calls `read_from_kafka_and_process` on the `kafka_trade_processor`
        to continuously read trade-related messages from Kafka and update positions
        in the application.
        """
        await self.kafka_trade_processor.read_from_kafka_and_process()

    async def market_termination_checker(self) -> None:
        """Calculates the time remaining in market closing and sleeps for that time.

        This function calculates the time remaining in market closing and sleeps for that time.
        Post that, it sets a flag which signals the other async processes to stop as well.
        """
        seconds_until_market_termination: int = max(
            0, (self.market_closing_time - get_local_timestamp()).seconds
        )
        await asyncio.sleep(seconds_until_market_termination)
        self.one_second_exit_application_logger.info(
            "Market closed, stopping One Exit Application."
        )
        self.is_market_closed.set_result(True)
        self.kafka_trade_processor.is_market_closed.set_result(True)

    async def one_second_exit_application_runner(self) -> None:
        """
        Runs the core event loop for the One Second Exit Application.

        Behavior:
            - `fetch_data_and_check_exit_conditions()`:
                - Periodically fetches the latest market data.
                - Evaluates and executes exit conditions for open positions.
            - `consume_kafka_messages_and_update_positions()`:
                - Continuously reads trade-related messages from Kafka.
                - Updates trade positions based on received messages.
        """
        await asyncio.gather(
            self.fetch_data_and_check_exit_conditions(),
            self.consume_kafka_messages_and_update_positions(),
            self.market_termination_checker(),
        )

    def run(self, *args: Any, **kwargs: Any) -> None:
        """
        Starts the One Second Exit Application.

        This method initializes and runs the `one_second_exit_application_runner` coroutine
        and ensure proper execution within an asyncio event loop.
        """
        asyncio.run(self.one_second_exit_application_runner())
