from minio import Minio  # type: ignore
import pandas as pd
import pickle
from io import Bytes<PERSON>
from typing import Tuple, List
from core.helpers.configstore import (
    EXCHANGE_TYPE,
    EXECUTION_LEVEL,
    MINIO_END_POINT,
    MINIO_ACCESS_KEY,
    MINIO_SECRET_KEY,
    MINIO_BUCKET_NAME,
    WEBHOOK,
)
from typing import Any
from core.runners.runner_base import RunnerBase
from exchange.helpers.mixins import TradeQueryMixin
from core.helpers.alerting import send_message_to_gspace
from balte.utility import add_strategy_to_module
from core.helpers.utils import get_local_date
from balte.utility import handle_expiry
import balte.balte_config


class LiveTradePickleDbRunnerBase(RunnerBase, TradeQueryMixin):
    def __init__(
        self, allowed_segments: List[str], allowed_universes: List[str]
    ) -> None:
        super().__init__()
        self.allowed_segments = allowed_segments
        self.allowed_universes = allowed_universes

    def extract_date_fxn(self, ID: int, universe: str) -> str:
        """Extracts the expiry dates from the ID column in a pickle dataframe in
        YYYY-MM-DD format.

        Args:
            ID (int): ID value of each trade in pickle
            universe (str): universe of each trade in pickle

        Returns:
            str: Extracted date in the format YYYY-MM-DD
        """

        extract_time = handle_expiry(ID, universe)
        extract_date: str = extract_time.date().strftime("%Y-%m-%d")

        return extract_date

    def check_mismatch(
        self, df_live: pd.DataFrame, df_pickles: pd.DataFrame, strategy_name: str
    ) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Checks trades present in live db but not in pickles and vice-versa

        Args:
            df_live (pd.DataFrame): Contains trade records in database DB live
            df_pickles (pd.DataFrame): Contains trade records in pickle
            strategy_name (str): checks is it master_cluster strategy or other

        Returns:
            Tuple[pd.DataFrame, pd.DataFrame]: Tuple containing unmatched records
            in the database DB, unmatched records in the pickles
        """
        if strategy_name in ["master_cluster", "cluster_um_master", "cluster_cag"]:
            s_pickle = set(df_pickles["order_id"])
            s_specific = set(df_live["TRADEID"])

            unmatched_p = s_pickle - s_specific
            unmatched_pickles = df_pickles[df_pickles.order_id.isin(unmatched_p)]
            unmatched_pickles["STRATEGY"] = strategy_name
            unmatched_database = pd.DataFrame()

        else:
            specific_df = df_live[df_live["STRATEGY"] == strategy_name]
            specific_df = specific_df.sort_values(by="TRADEID")

            s_pickle = set(df_pickles["order_id"])
            s_specific = set(specific_df["TRADEID"])

            unmatched_p = s_pickle - s_specific
            unmatched_db = s_specific - s_pickle

            unmatched_pickles = df_pickles[df_pickles.order_id.isin(unmatched_p)]
            unmatched_database = df_live[df_live.TRADEID.isin(unmatched_db)]

            unmatched_pickles["STRATEGY"] = strategy_name

        return unmatched_database, unmatched_pickles

    def check_expiry(
        self, df_live: pd.DataFrame, df_pickles: pd.DataFrame, strategy_name: str
    ) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Checks expired trades in DB and pickles

        Args:
            df_live (pd.DataFrame): Contains all trade records in database DB live
            df_pickles (pd.DataFrame): Contains all trade records in pickle
            strategy_name (str): checks is it master_cluster strategy or other

        Returns:
            Tuple[pd.DataFrame, pd.DataFrame]: Tuple containing expiry records
            in the database DB, expiry records in the pickles.
        """

        current_date = get_local_date().strftime("%Y-%m-%d")
        if not df_live.empty:
            df_live = df_live[df_live["SEGMENT"].isin(self.allowed_segments)]
        expiry_in_database = pd.DataFrame()
        if not df_live.empty:
            expiry_in_database = df_live[
                pd.to_datetime(df_live["EXPIRY"], format="%d-%b-%Y").astype(str)
                < current_date
            ]
        if not df_pickles.empty:
            options_universe = [
                universe
                for universe in self.allowed_universes
                if universe in balte.balte_config.OPTIONS_UNIVERSE
            ]
            df_pickles = df_pickles[df_pickles["universe"].isin(options_universe)]
        expiry_in_pickles = pd.DataFrame()
        if not df_pickles.empty:
            expiry_in_pickles = df_pickles[
                df_pickles.apply(
                    lambda x: self.extract_date_fxn(x["ID"], x["universe"]), axis=1
                )
                < current_date
            ]

        expiry_in_pickles["STRATEGY"] = strategy_name

        return expiry_in_database, expiry_in_pickles

    def send_mail_by_aggregating_errors(
        self, errors_trade_list: List[pd.DataFrame], minio_client: Minio
    ) -> None:
        """Generates notifications if errors are found in the given list of DataFrames

        Args:
            errors_trade_list (List[pd.DataFrame]): A list of 4 DataFrames containing errors:
            unmatched in pickles, unmatched in db, expired trades in pickle, expired trades in db

        Returns:
            None
        """

        if all(dataframe.empty for dataframe in errors_trade_list):
            return

        errors_trade_summary_list = []

        csv_file_paths = [
            "misc_data/unmatched_trade_inpickles_live.csv",
            "misc_data/unmatched_trade_indatabase_live.csv",
            "misc_data/expired_trade_inpickles_live.csv",
            "misc_data/expired_trade_indatabase_live.csv",
        ]

        for i, dataframe in enumerate(errors_trade_list):
            errors_trade_summary_list.append(
                dataframe.STRATEGY.value_counts().to_frame()
            )
            if not dataframe.empty:
                csv_bytes = dataframe.to_csv().encode("utf-8")
                csv_buffer = BytesIO(csv_bytes)
                minio_client.put_object(
                    MINIO_BUCKET_NAME,
                    csv_file_paths[i],
                    data=csv_buffer,
                    length=len(csv_bytes),
                    content_type="application/csv",
                )
        # Generate tables for each summary
        tables = ""

        error_type = [
            "Unmatched trades in all Pickles",
            "Unmatched trades in DB",
            "Expired trades in all Pickles",
            "Expired trades in DB",
        ]

        for i, dataframe in enumerate(errors_trade_summary_list):
            tables += f"{error_type[i].upper()}\n\n"
            table = dataframe.to_markdown(index=False, tablefmt="github")
            tables += table + "\n\n"

        body = "```" + tables + "```"
        send_message_to_gspace(body, WEBHOOK)

    @staticmethod
    def _concat_list_to_df_if_not_empty(list_df: List[pd.DataFrame]) -> pd.DataFrame:
        """Function to concat list of dataframes to a single dataframe if not empty

        Args:
            list_df (List[pd.DataFrame]): list of dataframes

        Returns:
            pd.DataFrame: combined dataframe
        """
        if len(list_df):
            return pd.concat(list_df)
        return pd.DataFrame()

    def run(self, *args: Any, **kwargs: Any) -> None:
        """ """
        minio_client = Minio(
            MINIO_END_POINT,
            access_key=MINIO_ACCESS_KEY,
            secret_key=MINIO_SECRET_KEY,
            secure=False,
        )
        df_live = self.get_all_live_trades()
        # empty lists initialised to accomodate dataframes so that all
        # dataframes can later be concatenated in a single step
        unmatched_trade_in_database_list = []
        unmatched_trade_in_pickles_list = []
        expiry_trade_in_database_list = []
        expiry_trade_in_pickles_list = []
        for fileobj in minio_client.list_objects(MINIO_BUCKET_NAME, "pickles/"):
            # extract strat name from pickle file path
            filename = fileobj.object_name.split("/")[-1]
            date = filename.split("_")[-2]
            date_index = filename.find(date)
            strat = filename[: date_index - 1]
            # add strat to sys.modules
            add_strategy_to_module(strat)
            if (
                (not filename.endswith(("00-00")))
                or ("watchdog" in filename)
                or ("hammer_v2" in filename)
                or ("toti_cache" in filename)
                or (get_local_date().strftime("%Y-%m-%d") not in filename)
            ):
                continue

            obj = pickle.loads(
                minio_client.get_object(MINIO_BUCKET_NAME, "pickles/" + filename).data
            )
            obj._cache_tradelog_is_valid = False
            df_pickles = obj.get_tradelog()
            df_pickles = df_pickles.sort_values(by="order_id")

            if "master_cluster" in filename:
                strategy_name = "master_cluster"
            elif "cluster_um_master" in filename:
                strategy_name = "cluster_um_master"
            elif "cluster_cag" in filename:
                strategy_name = "cluster_cag"
            else:
                strategy_name = obj._order.strategy_name

            # check mismatch between pickles and live trades
            unmatched_in_database, unmatched_in_pickles = self.check_mismatch(
                df_live, df_pickles, strategy_name
            )
            unmatched_trade_in_database_list.append(unmatched_in_database)
            unmatched_trade_in_pickles_list.append(unmatched_in_pickles)

            # check expiry trades in pickles and live trades
            expiry_in_database, expiry_in_pickles = self.check_expiry(
                df_live, df_pickles, strategy_name
            )
            expiry_trade_in_database_list.append(expiry_in_database)
            expiry_trade_in_pickles_list.append(expiry_in_pickles)

        unmatched_trade_in_database = self._concat_list_to_df_if_not_empty(
            list_df=unmatched_trade_in_database_list
        )
        unmatched_trade_in_pickles = self._concat_list_to_df_if_not_empty(
            list_df=unmatched_trade_in_pickles_list
        )
        expiry_trade_in_database = self._concat_list_to_df_if_not_empty(
            list_df=expiry_trade_in_database_list
        )
        expiry_trade_in_pickles = self._concat_list_to_df_if_not_empty(
            list_df=expiry_trade_in_pickles_list
        )

        errors_trade_list = [
            unmatched_trade_in_pickles,
            unmatched_trade_in_database,
            expiry_trade_in_pickles,
            expiry_trade_in_database,
        ]

        self.send_mail_by_aggregating_errors(errors_trade_list, minio_client)
