import balte
from balte.utility_inbuilt import balte_initializer
import balte.balte_config
from airflow.operators.python import BranchPythonOperator
from core.version_compat import IS_PY311_PLUS
from airflow.models import DAG
from core.helpers.configstore import <PERSON><PERSON><PERSON><PERSON><PERSON>_TYPE
from core.helpers.utils import get_local_date

if IS_PY311_PLUS:
    from airflow.operators.empty import EmptyOperator as DummyOperator  # type: ignore
else:
    from airflow.operators.dummy import DummyOperator  # type: ignore


def check_trading_holiday(exchange: str) -> str:
    """checks whether today is a holiday in a given `exchange`.
    Used in an airflow context as a BranchPythonOperator, i.e.
    return value specifies the task_id of downstream_tasks to do next

    Args:
        exchange (str): passed to balte_initializer to make appropriate TIMEZONE and ALL_DATES

    Returns:
        str: _description_
    """
    balte_initializer(exchange.lower())
    today = get_local_date()
    if today not in balte.balte_config.ALL_DATES:
        return "holiday"
    else:
        return "trading_day"


def get_trading_day_task(dag: DAG) -> DummyOperator:
    """set the return value of this as upstream to any other airflow tasks
    you are creating if you only want them to run on non-holidays. 
    Holidays are checked according to balte.balte_config.ALL_DATES. \\
    Example Usage: get_trading_day_task(dag) >> PythonOperator(...)

    Args:
        dag (DAG): the airflow dag in whose context we want to create these tasks

    Returns:
        DummyOperator: The operator to set upstream to your tasks
    """
    holiday = DummyOperator(
        task_id="holiday",
        dag=dag,
    )

    trading_day = DummyOperator(
        task_id="trading_day",
        dag=dag,
    )

    trading_holiday_check = BranchPythonOperator(
        task_id="trading_holiday_check",
        python_callable=check_trading_holiday,
        op_kwargs={"exchange": EXCHANGE_TYPE},
        dag=dag,
    )

    trading_holiday_check >> [trading_day, holiday]

    return trading_day
