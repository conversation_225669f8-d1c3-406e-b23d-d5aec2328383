from core.dags.dag_builder_base import DagBuilderBase
from airflow.models import DAG
import pandas as pd
from datetime import timedelta
from typing import Optional, Any
from airflow.operators.python import PythonOperator
from core.helpers.configstore import (
    EXCHANGE_TYPE,
    EXECUTION_LEVEL,
)
from exchange.helpers.mixins import TradeQueryMixin
from core.helpers.utils import get_local_date


class OlderTradeAlertDagBuilderBase(DagBuilderBase, TradeQueryMixin):
    def __init__(self, dag_id: str, schedule_interval: str) -> None:
        super().__init__(
            dag_id=dag_id,
            schedule_interval=schedule_interval,
        )

    def get_older_trades(
        self,
        threshold: float,
    ) -> Optional[pd.DataFrame]:
        threshold_date = get_local_date() - timedelta(days=threshold)
        sql_live = f"SELECT * FROM {EXCHANGE_TYPE}_{EXECUTION_LEVEL} WHERE ENTRY_TIMESTAMP_META < %s "
        df_live_older = self.filter_trades(
            sql_live,
            params=[
                threshold_date.strftime("%Y-%m-%d"),
            ],
        )

        if df_live_older.empty:
            return None
        else:
            return df_live_older

    def check_older_trades(self) -> None:
        older_trades = self.get_older_trades(threshold=10)

        if (older_trades is not None) and (not older_trades.empty):
            print("Trades Older than 10 days found in live")
            print(older_trades)
            raise ValueError("Older Trades Found")

    def build_dag(self) -> DAG:
        dag = DAG(
            dag_id=self.dag_id,
            schedule_interval=self.schedule_interval,
            default_args=self.dag_default_args,
            catchup=self.dag_catchup,
        )
        older_trade = PythonOperator(
            task_id="check_oldertrades",
            python_callable=self.check_older_trades,
            dag=dag,
        )
        return dag
