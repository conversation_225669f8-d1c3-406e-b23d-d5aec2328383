"""
The main trading dag for live trading.

Has nodes for slaves, cluster and watchdog in it.
"""

from core.dags.dag_builder_base import DagBuilderBase
from core.dags.utils import get_trading_day_task
from core.helpers.configstore import (
    EXCHANGE_TYPE,
    EXECUTION_LEVEL,
    CONDA_ENV_NAME,
)
from typing import Optional, Any
from airflow.models import DAG
from airflow.operators.bash import BashOperator


class BalteTradingDagBuilderBase(DagBuilderBase):
    def __init__(self, dag_id: str, schedule_interval: str) -> None:
        super().__init__(
            dag_id=dag_id,
            schedule_interval=schedule_interval,
        )

    def build_dag(self) -> DAG:
        dag = DAG(
            dag_id=self.dag_id,
            schedule_interval=self.schedule_interval,
            default_args=self.dag_default_args,
            catchup=self.dag_catchup,
        )

        get_trading_day_task(dag) >> [
            BashOperator(
                task_id=f"{EXCHANGE_TYPE}_{EXECUTION_LEVEL}_SLAVE",
                bash_command=f"/opt/conda/envs/{CONDA_ENV_NAME}/bin/python /app/src/exchange/runners/slave.py",
                dag=dag,
            ),
            BashOperator(
                task_id=f"{EXCHANGE_TYPE}_{EXECUTION_LEVEL}_WATCHDOG",
                bash_command=f"/opt/conda/envs/{CONDA_ENV_NAME}/bin/python /app/src/exchange/runners/watchdog_runner.py",
                dag=dag,
            ),
            BashOperator(
                task_id=f"{EXCHANGE_TYPE}_{EXECUTION_LEVEL}_CLUSTER",
                bash_command=f"/opt/conda/envs/{CONDA_ENV_NAME}/bin/python /app/src/exchange/runners/cluster.py",
                dag=dag,
            ),
        ]

        return dag
