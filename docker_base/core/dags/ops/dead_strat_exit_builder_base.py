"""
This dag is responsible for sending exits to strategies that have been marked dead. Usually they might also be holding
some position, so we have to handle them separately.

Note: Please check and change the time for exit generation
"""

import os
import pandas as pd
import pymysql
import logging
import time
from typing import List

from core.helpers.configstore import (
    EXCHANGE_TYPE,
    EXECUTION_LEVEL,
    OMS_HOST,
    OMS_PORT,
    SEGMENT,
    EXCHANGE_TYPE_MAPPING,
)

import balte.balte_config
from balte.enums import ExchangeType

import toti
import toti.toti_config

from core.dags.dag_builder_base import DagBuilderBase
from core.dags.utils import get_trading_day_task
from core.helpers.utils import get_local_timestamp, get_local_date
from airflow.models import DAG
from airflow.operators.python import PythonOperator
from exchange.helpers.mixins import TradeQueryMixin, OmsOpsMixin


class DeadStratExitDagBuilderBase(DagBuilderBase, TradeQueryMixin, OmsOpsMixin):
    def __init__(self, dag_id: str, schedule_interval: str) -> None:
        super().__init__(
            dag_id=dag_id,
            schedule_interval=schedule_interval,
        )
        # If exit fails, exception is raised rather than retrying
        toti.toti_config.DISABLE_EXIT_RETRIES = True
        self.automated_exit_strat_logger = logging.getLogger()
        self.table_name = f"{EXCHANGE_TYPE}_{EXECUTION_LEVEL}"

    def exit_trades(
        self,
        exit_df: pd.DataFrame,
        exit_timestamp: pd.Timestamp,
        toti_obj: toti.Toti,
    ) -> None:
        """
        Sends exit orders for each position in 'exit_df' dataframe

        Args:
            exit_df (pd.DataFrame): dataframe of positions to be exited
            exit_timestamp (pd.Timestamp): timestamp at which exit should take place
            toti_obj (toti.Toti): toti object to send exit orders
        """
        for row in exit_df.itertuples():
            # handling futures
            balte_exit_request = self.send_exit_trade(
                trade_info=row, exit_timestamp_meta=exit_timestamp, toti_obj=toti_obj
            )
            self.automated_exit_strat_logger.info(
                f"Exited {balte_exit_request.__str__()}"
            )

    ## automated_exit_strat logger ##
    def setup_logger(self, table_name: str) -> logging.Logger:
        LOG_DIR = "/opt/balte_live/log/exit_strat_log"
        LOG_FILE = os.path.join(LOG_DIR, f"{table_name}.log")
        self.automated_exit_strat_logger = logging.getLogger()
        self.automated_exit_strat_logger.setLevel(logging.INFO)
        fh = logging.FileHandler(LOG_FILE)
        fh.setLevel(logging.INFO)
        self.automated_exit_strat_logger.addHandler(fh)
        print(f" {table_name} logger generated")
        return self.automated_exit_strat_logger

    def get_dead_strats_list(self) -> List[str]:
        from analytics_api import AnalyticsAPI

        api = AnalyticsAPI(mode=EXECUTION_LEVEL)
        dead_strategy_list = api.get_dead_strategies(
            segment=SEGMENT.split(","), exchange=[EXCHANGE_TYPE.upper()]
        )
        dead_strategy_list = [
            "BaLTE_" + strategy
            for strategy in dead_strategy_list
            if "cluster" not in strategy
        ]

        if not dead_strategy_list:
            self.automated_exit_strat_logger.info("Dead strategy list is empty")
            return []

        balte_oms = self.get_all_live_trades()
        live_strategies = balte_oms["STRATEGY"].unique().tolist()

        inactivated_strategy_list = [
            x for x in live_strategies if x in dead_strategy_list
        ]

        return sorted(inactivated_strategy_list)

    def decide_exit_timestamp(self) -> pd.Timestamp:
        """
        this function is used to determine when to send exit for slave level trades.
        """
        local_date = get_local_date()
        if local_date in balte.balte_config.ALL_DATES:
            return local_date.replace(hour=9, minute=17)
        raise Exception("today is not in ALL_DATES")

    def decide_exit_timestamp_meta(self) -> pd.Timestamp:
        """
        this function is used to determine what to keep exit_timestamp in db for corresponding exit.
        """
        local_date = get_local_date()
        if local_date in balte.balte_config.ALL_DATES:
            return local_date.replace(hour=9, minute=20)
        raise Exception("today is not in ALL_DATES")

    def exit_process(self) -> None:
        self.automated_exit_strat_logger = self.setup_logger(self.table_name)
        # main process to exit dead strats live trades
        inactivated_strategy_list = self.get_dead_strats_list()
        if (inactivated_strategy_list is not None) and (
            len(inactivated_strategy_list) > 0
        ):
            balte_oms = self.get_all_live_trades()
            exit_timestamp = self.decide_exit_timestamp()
            exit_timestamp_meta = self.decide_exit_timestamp_meta()
            strategy_count_dict = {}
            while get_local_timestamp() < exit_timestamp:
                time.sleep(10)
            for strategy in inactivated_strategy_list:
                strategy_exit_df = balte_oms[balte_oms["STRATEGY"] == str(strategy)]
                segment_list = strategy_exit_df.SEGMENT.unique().tolist()
                strategy_count_dict[strategy] = len(strategy_exit_df)
                for segment in segment_list:
                    toti.Toti.set_production_mode(f"{OMS_HOST}:{OMS_PORT}")
                    exchange_type_enum: ExchangeType = ExchangeType(
                        EXCHANGE_TYPE_MAPPING[EXCHANGE_TYPE.lower()]
                    )
                    toti_obj = toti.Toti(exchange_type_enum)
                    exit_df = strategy_exit_df[strategy_exit_df.SEGMENT == segment]
                    self.exit_trades(
                        exit_df=exit_df,
                        exit_timestamp=exit_timestamp_meta,
                        toti_obj=toti_obj,
                    )
            print(strategy_count_dict)

    def build_dag(self) -> DAG:
        dag = DAG(
            dag_id=self.dag_id,
            schedule_interval=self.schedule_interval,
            default_args=self.dag_default_args,
            catchup=self.dag_catchup,
        )

        get_trading_day_task(dag) >> PythonOperator(
            task_id="exiting_dead_strat_live_table",
            python_callable=self.exit_process,
            dag=dag,
        )

        return dag
