from core.runners.runner_balte import RunnerBaLTE
import streamlit as st
from typing import List, Any, TYPE_CHECKING
import logging

for handler in logging.root.handlers[:]:
    logging.root.removeHandler(handler)

logging.basicConfig(
    filename="/opt/exit_portal/log/exit_portal.log",
    filemode="a",
    format="%(asctime)s.%(msecs)03d %(levelname)s %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)

if TYPE_CHECKING:
    from streamlit.navigation.page import StreamlitPage


class StreamlitAppRunnerBase(RunnerBaLTE):
    def __init__(self, pages: List["StreamlitPage"]) -> None:
        super().__init__()
        self.pages = pages

    def run(self, *args: Any, **kwargs: Any) -> None:
        pg = st.navigation(self.pages)
        pg.run()
