import streamlit as st
from core.apps.exit_portal_components.operations_base import OperationBase
from exchange.helpers.mixins import TradeQueryMixin, OmsOpsMixin
import pandas as pd
from typing import Optional, List
import pytz
from toti import Toti
import logging
from core.helpers.utils import get_local_timestamp


class ExitSlavesMainTradesOperationBase(OperationBase, TradeQueryMixin, OmsOpsMixin):
    def __init__(self, toti_obj: <PERSON><PERSON>, username: str) -> None:
        super().__init__()
        self.toti_obj = toti_obj
        self.username = username

    def getting_slave_wise_trades(
        self,
        slave_names: List[str],
    ) -> pd.DataFrame:
        """
        this will exit main trades corresponding to slave name passed

        Args:
            slave_names List[str]: these slaves main cluster trades will get exit
        Returns:
            List[str]: returns tradeid list to exit
        """
        live_trades = self.get_all_live_trades()
        live_trades = live_trades[
            live_trades["SLAVE_NAME"].isin(slave_names)
            & live_trades["STRATEGY"].str.startswith("cluster")
        ]
        return live_trades

    def render(self) -> None:
        slave_names = st.text_input("ENTER SLAVES NAME( COMMA SEPARATED ) ;- ")
        slave_names = slave_names.strip()
        slave_names_list = slave_names.split(",")
        slave_names_list = [item.strip() for item in slave_names_list]
        if st.button("EXIT THESE SLAVE'S MAIN CLUSTER TRADE"):
            st.session_state.exit_button_clicked = True

        if ("exit_button_clicked" in st.session_state) and (
            st.session_state.exit_button_clicked == True
        ):
            trades_to_exit = self.getting_slave_wise_trades(slave_names_list)
            if trades_to_exit.empty:
                st.write("No cluster trades present corresponding to these slaves")
            else:
                st.write("THESE TRADE WILL GET EXIT")
                st.write(trades_to_exit)
            exit_timestamp_insertion = (
                get_local_timestamp().round("min").replace(second=0)
            )
            if st.button("YES , EXIT THESE ALL") and not trades_to_exit.empty:
                trade_ids_exited = list(trades_to_exit.TRADEID)
                try:
                    st.session_state.exit_button_clicked = False
                    self.send_exit_trades(
                        trades_to_exit=trades_to_exit,
                        exit_timestamp_meta=exit_timestamp_insertion,
                        toti_obj=self.toti_obj,
                        username=self.username,
                    )
                    logging.info(
                        "exit process completed for slave's main trades {} : {} by user: {}".format(
                            slave_names_list, trade_ids_exited, self.username
                        )
                    )
                    st.write(
                        "exit process completed for slave's main trades {} : {}".format(
                            slave_names_list, trade_ids_exited
                        )
                    )
                except Exception as e:
                    logging.exception(
                        "exit process failed with exception: {} for {} : {} by user: {} with exception: {}".format(
                            repr(e),
                            slave_names_list,
                            trade_ids_exited,
                            self.username,
                            repr(e),
                        )
                    )
                    st.write(f"exit process failed with an Exception: {e}")
            else:
                logging.info("No action taken")
                pass
