import streamlit as st
from core.apps.exit_portal_components.operations_base import OperationB<PERSON>
from core.helpers.configstore import (
    MINIO_END_POINT,
    MINIO_ACCESS_KEY,
    MINIO_SECRET_KEY,
    MINIO_BUCKET_NAME,
)
from minio import Minio  # type: ignore


class DeleteStrategyPicklesOperationBase(OperationBase):
    def __init__(self) -> None:
        super().__init__()

    def render(self) -> None:
        minio_client = Minio(
            MINIO_END_POINT,
            access_key=MINIO_ACCESS_KEY,
            secret_key=MINIO_SECRET_KEY,
            secure=False,
        )
        file_objects = minio_client.list_objects(MINIO_BUCKET_NAME, prefix="pickles/")
        file_list = [file.object_name for file in file_objects]
        pickle_list = [x.split("/")[-1] for x in file_list]
        strategy_list = set()
        for file in pickle_list:
            strategy_list.add(file[:-17])

        pickle_deletion_option = st.radio(
            "Select a pickle deletion option",
            [
                "Delete all pickles",
                "Delete all pickles of a strategy",
                "Delete strategy pickle of given timestamp",
            ],
        )
        if pickle_deletion_option == "Delete all pickles":
            st.write(
                "Clicking Send Button will delete pickles of all strategies. Do you wish to proceed?"
            )
            if st.button("SEND"):
                deletion_count = 0
                for file in minio_client.list_objects(
                    MINIO_BUCKET_NAME,
                    prefix="pickles/",
                    recursive=True,
                    include_version=True,
                ):
                    if "toti_cache_" not in file.object_name:
                        deletion_count += 1
                        minio_client.remove_object(
                            MINIO_BUCKET_NAME,
                            object_name=file.object_name,
                            version_id=file.version_id,
                        )
                st.success(f"{deletion_count} pickles have been deleted")
        elif pickle_deletion_option == "Delete all pickles of a strategy":
            strategy_name = st.selectbox(
                "Select name of the strategy", options=strategy_list
            )
            if strategy_name:
                st.write(
                    f"Clicking Send Button will delete all pickles of {strategy_name}. Do you wish to proceed?"
                )
            if st.button("SEND"):
                if strategy_name:
                    deletion_count = 0
                    for file in minio_client.list_objects(
                        MINIO_BUCKET_NAME,
                        prefix=f"pickles/{strategy_name}_",
                        recursive=True,
                        include_version=True,
                    ):
                        minio_client.remove_object(
                            MINIO_BUCKET_NAME,
                            object_name=file.object_name,
                            version_id=file.version_id,
                        )
                        deletion_count += 1
                    st.success(f"{deletion_count} pickles have been deleted")
                else:
                    st.error("No strategy name given")
        elif pickle_deletion_option == "Delete strategy pickle of given timestamp":
            pickle_name = st.selectbox(
                "Select pickle for deletion FORMAT('STRATEGY_NAME_YYYY-MM-DD_HH-MM') :-",
                options=pickle_list,
            )
            if pickle_name:
                st.write(
                    f"Clicking Send Button will delete the pickle {pickle_name}. Do you wish to proceed?"
                )
            if st.button("SEND"):
                if pickle_name:
                    deletion_count = 0
                    for file in minio_client.list_objects(
                        MINIO_BUCKET_NAME,
                        prefix=f"pickles/{pickle_name}",
                        recursive=True,
                        include_version=True,
                    ):
                        deletion_count += 1
                        minio_client.remove_object(
                            MINIO_BUCKET_NAME,
                            object_name=file.object_name,
                            version_id=file.version_id,
                        )
                    st.success(f"{deletion_count} pickles have been deleted")
                else:
                    st.error("Strategy name or timestamp is missing")

        else:
            pass
