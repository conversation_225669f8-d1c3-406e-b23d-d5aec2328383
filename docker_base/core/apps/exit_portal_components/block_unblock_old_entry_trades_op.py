import streamlit as st
from toti import Toti
from toti.rpc import oms_pb2
from core.apps.exit_portal_components.operations_base import OperationBase


class BlockUnblockOldEntryTradesOperationBase(OperationBase):
    def __init__(self, toti_obj: Toti) -> None:
        super().__init__()
        self.toti_obj = toti_obj

    def render(self) -> None:
        if "block_trade_button_clicked" not in st.session_state:
            st.session_state["block_trade_button_clicked"] = False
        if "unblock_trade_button_clicked" not in st.session_state:
            st.session_state["unblock_trade_button_clicked"] = False

        # when block button was called
        def callback_blocked() -> None:
            st.session_state.block_trade_button_clicked = True

        # when unblock button was called
        def callback_unblocked() -> None:
            st.session_state.unblock_trade_button_clicked = True

        if (
            st.button("BLOCK", on_click=callback_blocked)
            or st.session_state.block_trade_button_clicked
        ):
            time_duration = st.text_input(
                "ENTER THE TIME (in mins) BEFORE WHICH ALL ENTRY TRADES SHOULD BE BLOCKED:- "
            )
            if time_duration:  # empty string in first render
                st.write(
                    f"All stale entry trades sent before last {int(time_duration)} mins will be rejected. Do you wish to proceed?"
                )
                if st.button("YES"):
                    st.session_state.block_trade_button_clicked = False
                    request = oms_pb2.StaleEntryTradeFilterRequest(
                        apply=True, allowed_trade_duration=int(time_duration)
                    )
                    ret_code = self.toti_obj.set_stale_entry_trade_filter(request)
                    st.write(ret_code)
                    if ret_code == 0:
                        st.success("Request sent sucessfully")
                    else:
                        st.error("Stale Entry Trade Filter Request failed")
        if (
            st.button("UNBLOCK", on_click=callback_unblocked)
            or st.session_state.unblock_trade_button_clicked
        ):
            st.write(
                "Stale entry trade filter will be reset and all entry trades will be accepted. Please confirm."
            )
            if st.button("CONFIRM"):
                st.session_state.unblock_trade_button_clicked = False
                request = oms_pb2.StaleEntryTradeFilterRequest(
                    apply=False, allowed_trade_duration=0
                )
                ret_code = self.toti_obj.set_stale_entry_trade_filter(request)
                st.write(ret_code)
                if ret_code == 0:
                    st.success("Request sent sucessfully")
                else:
                    st.error("Stale Entry Trade Filter Request failed")
