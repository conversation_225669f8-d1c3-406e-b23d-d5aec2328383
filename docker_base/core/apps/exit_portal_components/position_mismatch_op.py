import streamlit as st
from core.apps.exit_portal_components.operations_base import OperationBase
from exchange.helpers.mixins import TradeQueryMixin


class PositionMismatchOperationBase(OperationBase, TradeQueryMixin):
    def __init__(self) -> None:
        super().__init__()

    def render(self) -> None:
        mismatched_df = self.get_mismatch_from_live_sheet()
        st.write("THE MISMATCHED POSITIONS ARE AS FOLLOWS:-")
        st.write(mismatched_df)
