import streamlit as st
from core.apps.exit_portal_components.operations_base import OperationBase
from core.helpers.configstore import EXCHANGE_TYPE, EXECUTION_LEVEL
from exchange.helpers.mixins import TradeQueryMixin, OmsOpsMixin
from core.helpers.utils import get_local_timestamp
import pandas as pd
import balte.balte_config
import logging
from typing import List, Any
from toti import Toti
from balte.utility_inbuilt import get_next_business_date
import datetime


class ExitPositionOperationBase(OperationBase, TradeQueryMixin, OmsOpsMixin):
    def __init__(self, toti_obj: Toti, username: str) -> None:
        super().__init__()
        self.toti_obj = toti_obj
        self.username = username

    # We have added underscore in front of self, so that
    # st.cache_resource ignores that param for caching
    @st.cache_resource
    def exit_positions_with_strategy_name(
        _self, strategy_name: str, exit_timestamp_meta: pd.Timestamp
    ) -> int:
        trades_to_exit = _self.filter_trades(
            query=f"SELECT * FROM {EXCHANGE_TYPE}_{EXECUTION_LEVEL} WHERE STRATEGY = (%s)",
            params=[strategy_name],
        )
        _self.send_exit_trades(
            trades_to_exit=trades_to_exit,
            exit_timestamp_meta=exit_timestamp_meta,
            toti_obj=_self.toti_obj,
            username=_self.username,
        )
        return len(trades_to_exit)

    @st.cache_resource
    def exit_positions_with_strategy_and_symbol_name(
        _self, strategy_name: str, symbol: str, exit_timestamp_meta: pd.Timestamp
    ) -> int:
        trades_to_exit = _self.filter_trades(
            query=f"SELECT * FROM {EXCHANGE_TYPE}_{EXECUTION_LEVEL} WHERE STRATEGY = (%s) AND SYMBOL = (%s)",
            params=[strategy_name, symbol],
        )
        _self.send_exit_trades(
            trades_to_exit=trades_to_exit,
            exit_timestamp_meta=exit_timestamp_meta,
            toti_obj=_self.toti_obj,
            username=_self.username,
        )
        return len(trades_to_exit)

    @st.cache_resource
    def exit_positions_with_symbol(
        _self,
        symbol_name: str,
        exit_timestamp_meta: pd.Timestamp,
    ) -> int:
        trades_to_exit = _self.filter_trades(
            query=f"SELECT * FROM {EXCHANGE_TYPE}_{EXECUTION_LEVEL} WHERE SYMBOL = (%s)",
            params=[symbol_name],
        )
        _self.send_exit_trades(
            trades_to_exit=trades_to_exit,
            exit_timestamp_meta=exit_timestamp_meta,
            toti_obj=_self.toti_obj,
            username=_self.username,
        )
        return len(trades_to_exit)

    @st.cache_resource
    def exit_positions_with_segment(
        _self,
        segment_name: str,
        exit_timestamp_meta: pd.Timestamp,
    ) -> int:
        trades_to_exit = _self.filter_trades(
            query=f"SELECT * FROM {EXCHANGE_TYPE}_{EXECUTION_LEVEL} WHERE SEGMENT = (%s)",
            params=[segment_name],
        )
        _self.send_exit_trades(
            trades_to_exit=trades_to_exit,
            exit_timestamp_meta=exit_timestamp_meta,
            toti_obj=_self.toti_obj,
            username=_self.username,
        )
        return len(trades_to_exit)

    @st.cache_resource
    def exit_positions_with_trade_ids(
        _self, trade_id_list: List[str], exit_timestamp_meta: pd.Timestamp
    ) -> List[str]:
        placeholders = ", ".join(["%s"] * len(trade_id_list))
        query = f"SELECT * FROM {EXCHANGE_TYPE}_{EXECUTION_LEVEL} WHERE TRADEID IN ({placeholders})"
        trades_to_exit = _self.filter_trades(
            query=query,
            params=trade_id_list,
        )
        _self.send_exit_trades(
            trades_to_exit=trades_to_exit,
            exit_timestamp_meta=exit_timestamp_meta,
            toti_obj=_self.toti_obj,
            username=_self.username,
        )

        trade_ids_exited = [str(x) for x in trades_to_exit.TRADEID]
        return trade_ids_exited

    def render(self) -> None:
        exit_options = st.radio(
            "SELECT EXIT CRITERIA :- ",
            (
                "EXIT TRADES",
                "EXIT STRATEGY",
                "EXIT SYMBOL",
                "EXIT SEGMENT",
            ),
        )
        default_timestamp_meta = get_local_timestamp().ceil("5Min")
        default_timestamp_meta = max(
            default_timestamp_meta, default_timestamp_meta.replace(minute=20, hour=9)
        )
        if default_timestamp_meta >= default_timestamp_meta.replace(hour=15, minute=30):
            default_timestamp_meta = get_next_business_date(
                pd.Timestamp(default_timestamp_meta.date())
            ).replace(hour=9, minute=20)
        if exit_options == "EXIT TRADES":
            trade_ids = st.text_input("ENTER TRADE IDS( COMMA SEPERATED ) :- ")
            exit_timestamp_meta_date: Any = st.date_input(  # can't tell mypy that a date will be returned not a date-range
                "ENTER exit_timestamp_meta_date :- ",
                value=default_timestamp_meta.date(),
            )
            exit_timestamp_meta_time = st.time_input(
                "ENTER exit_timestamp_meta_time :- ",
                value=default_timestamp_meta.time(),
                step=datetime.timedelta(minutes=1),
            )
            exit_timestamp_meta_datetime = datetime.datetime.combine(
                exit_timestamp_meta_date, exit_timestamp_meta_time
            )
            exit_timestamp_meta = pd.Timestamp(exit_timestamp_meta_datetime)
            if st.button("EXIT TRADES"):
                tradeid_list = []
                for tradeid in trade_ids.split(","):
                    tradeid_list.append(str(tradeid))

                try:
                    tradeid_exits = self.exit_positions_with_trade_ids(
                        tradeid_list,
                        exit_timestamp_meta,
                    )

                    logging.info(
                        "Exit process completed for {} : {} by user: {}".format(
                            exit_options, tradeid_exits, self.username
                        )
                    )

                    st.write(
                        "Exit process completed for Trade IDs = {}".format(
                            tradeid_exits
                        )
                    )
                except Exception as e:
                    logging.exception(
                        "Exit process failed with exception {} for {} : {} by user: {}".format(
                            repr(e), exit_options, tradeid_list, self.username
                        )
                    )

                    st.write(f"Exit process failed with an Exception: {repr(e)}")

        elif exit_options == "EXIT STRATEGY":
            # We have double authentication in case of exit_strategy to ask for extra confirmation if someone is exiting
            # a non-BaLTE strategy. This is needed because oms sends these orders to execution systems.
            # Streamlit has a weird behavior that every button click is an interaction and it reruns the whole code.
            # When this happens, the outer button state might go away when internal button is clicked. To ensure we
            # maintain this, we have to add manual button handling

            # Initialize state
            if "exit_strategy_button_clicked" not in st.session_state:
                st.session_state.exit_strategy_button_clicked = False

            # when outer button was called
            def callback() -> None:
                st.session_state.exit_strategy_button_clicked = True

            strat = st.text_input("ENTER STRATEGY :- ")
            symbol_name = st.text_input(
                "ENTER SYMBOL (LEAVE BLANK IF NOT APPLICABLE) :- "
            ).strip()
            strat = strat.strip()

            exit_timestamp_meta_date = st.date_input(
                "ENTER exit_timestamp_meta_date :- ",
                value=default_timestamp_meta.date(),
            )
            exit_timestamp_meta_time = st.time_input(
                "ENTER exit_timestamp_meta_time :- ",
                value=default_timestamp_meta.time(),
                step=datetime.timedelta(minutes=1),
            )
            exit_timestamp_meta_datetime = datetime.datetime.combine(
                exit_timestamp_meta_date, exit_timestamp_meta_time
            )
            exit_timestamp_meta = pd.Timestamp(exit_timestamp_meta_datetime)
            if (
                st.button("EXIT STRATEGY", on_click=callback)
                or st.session_state.exit_strategy_button_clicked
            ):
                try:
                    # checking if it starts with balte
                    if len(strat) < 6 or strat[:6] != "BaLTE_":
                        st.write(
                            "Are you sure strategy name is correct? As it should start with 'BaLTE_' "
                        )
                        if st.button("YES"):
                            st.session_state.exit_strategy_button_clicked = False
                            if symbol_name:
                                symbol = symbol_name.upper()
                                number_of_exits = (
                                    self.exit_positions_with_strategy_and_symbol_name(
                                        strategy_name=strat,
                                        symbol=symbol,
                                        exit_timestamp_meta=exit_timestamp_meta,
                                    )
                                )
                                logging.info(
                                    "Exit process completed for {} : {} with symbol = {} by user: {}".format(
                                        exit_options, strat, symbol, self.username
                                    )
                                )
                            else:
                                number_of_exits = (
                                    self.exit_positions_with_strategy_name(
                                        strategy_name=strat,
                                        exit_timestamp_meta=exit_timestamp_meta,
                                    )
                                )

                                logging.info(
                                    "Exit process completed for {} : {} by user: {}".format(
                                        exit_options, strat, self.username
                                    )
                                )

                            st.write(
                                f"Exit process completed with number of exit trades = {number_of_exits}"
                            )
                    else:
                        st.session_state.exit_strategy_button_clicked = False
                        if symbol_name:
                            symbol = symbol_name.upper()
                            number_of_exits = (
                                self.exit_positions_with_strategy_and_symbol_name(
                                    strategy_name=strat,
                                    symbol=symbol,
                                    exit_timestamp_meta=exit_timestamp_meta,
                                )
                            )
                            logging.info(
                                "Exit process completed for {} : {} with symbol = {} by user: {}".format(
                                    exit_options, strat, symbol, self.username
                                )
                            )
                        else:
                            number_of_exits = self.exit_positions_with_strategy_name(
                                strategy_name=strat,
                                exit_timestamp_meta=exit_timestamp_meta,
                            )

                            logging.info(
                                "Exit process completed for {} : {} by user: {}".format(
                                    exit_options, strat, self.username
                                )
                            )

                        st.write(
                            f"Exit process completed with number of exit trades = {number_of_exits}"
                        )
                except Exception as e:
                    logging.exception(
                        "Exit process failed with exception {} for {} : {} by user: {}".format(
                            repr(e), exit_options, strat, self.username
                        )
                    )

                    st.write(f"Exit process failed with an Exception: {repr(e)}")

        elif exit_options == "EXIT SYMBOL":
            symbol_name = st.text_input("ENTER SYMBOL :- ")
            symbol_name = (symbol_name.strip()).upper()

            exit_timestamp_meta_date = st.date_input(
                "ENTER exit_timestamp_meta_date :- ",
                value=default_timestamp_meta.date(),
            )
            exit_timestamp_meta_time = st.time_input(
                "ENTER exit_timestamp_meta_time :- ",
                value=default_timestamp_meta.time(),
                step=datetime.timedelta(minutes=1),
            )
            exit_timestamp_meta_datetime = datetime.datetime.combine(
                exit_timestamp_meta_date, exit_timestamp_meta_time
            )
            exit_timestamp_meta = pd.Timestamp(exit_timestamp_meta_datetime)
            if st.button("EXIT SYMBOL"):
                try:
                    if symbol_name not in balte.balte_config.symbol_to_balte_id.keys():
                        raise Exception(f"{symbol_name} is not a valid symbol!!")

                    number_of_exits = self.exit_positions_with_symbol(
                        symbol_name, exit_timestamp_meta
                    )

                    logging.info(
                        "Exit process completed for {} : {} by user: {}".format(
                            exit_options, symbol_name, self.username
                        )
                    )

                    st.write(
                        f"Exit process completed with number of exit trades = {number_of_exits}"
                    )
                except Exception as e:
                    logging.exception(
                        "Exit process failed with exception {} for {} : {} by user: {}".format(
                            repr(e), exit_options, symbol_name, self.username
                        )
                    )

                    st.write(f"Exit process failed with an Exception: {repr(e)}")

        elif exit_options == "EXIT SEGMENT":
            segment_name = st.text_input("ENTER SEGMENT :- ")
            segment_name = segment_name.strip()

            exit_timestamp_meta_date = st.date_input(
                "ENTER exit_timestamp_meta_date :- ",
                value=default_timestamp_meta.date(),
            )
            exit_timestamp_meta_time = st.time_input(
                "ENTER exit_timestamp_meta_time :- ",
                value=default_timestamp_meta.time(),
                step=datetime.timedelta(minutes=1),
            )
            exit_timestamp_meta_datetime = datetime.datetime.combine(
                exit_timestamp_meta_date, exit_timestamp_meta_time
            )
            exit_timestamp_meta = pd.Timestamp(exit_timestamp_meta_datetime)
            if st.button("EXIT SEGMENT"):
                try:
                    number_of_exits = self.exit_positions_with_segment(
                        segment_name, exit_timestamp_meta
                    )

                    logging.info(
                        "Exit process completed for {} : {} by user: {}".format(
                            exit_options, segment_name, self.username
                        )
                    )

                    st.write(
                        f"Exit process completed with number of exit trades = {number_of_exits}"
                    )
                except Exception as e:
                    logging.exception(
                        "Exit process failed with exception {} for {} : {} by user: {}".format(
                            repr(e), exit_options, segment_name, self.username
                        )
                    )

                    st.write(f"Exit process failed with an Exception: {repr(e)}")
        else:
            pass
