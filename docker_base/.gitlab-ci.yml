image: "*************:15050/devops/commonlibs/balte/stage_branch:latest"

# ========================
# Global Configuration
# ========================
variables:
  PYTHONPATH: "./pipeline:./src:/app/balte_live"
  DOCKER_AUTH_CONFIG: $DOCKER_AUTH_CONFIG
  GIT_SUBMODULE_STRATEGY: recursive
  GIT_SUBMODULE_FORCE_HTTPS: "true"
  BUILD_CONDA_ENV: "env_balte_1.0.0"

stages:
  - Verify_Template_Sync
  - Linting
  - Typing
  - Testing
  - Coverage
  - Publish


# ========================
# Reusable Templates
# ========================
.default_rules: &default_rules
  rules:
    - if: $CI_PROJECT_NAME != "docker_template"

.setup_template: &setup
  parallel:
    matrix:
      - CONDA_ENV_NAME: ["env_balte_0.0.3", "env_balte_1.0.0"]
  before_script:
    - set -u
    - echo $CONDA_ENV_NAME
    - source activate $BUILD_CONDA_ENV
    - pip install xxhash==3.5.0
    - python build.py --src . --build /opt/build
    - cd /opt/build
    - source activate "${CONDA_ENV_NAME:-$BUILD_CONDA_ENV}"
    - FILE=pipeline/.ci/requirements/${CI_JOB_STAGE}/common.txt; if [ -f $FILE ]; then pip install -r $FILE; fi 
    - FILE=pipeline/.ci/requirements/${CI_JOB_STAGE}/${CONDA_ENV_NAME}.txt; if [ -f $FILE ]; then pip install -r $FILE; fi 
    - FILE=pipeline/.ci/scripts/${CI_JOB_STAGE}/common.sh; if [ -f $FILE ]; then source $FILE; fi 
    - FILE=pipeline/.ci/scripts/${CI_JOB_STAGE}/${CONDA_ENV_NAME}.sh; if [ -f $FILE ]; then source $FILE; fi 


# ========================
# Jobs
# ========================

Verify_Template_Sync:
  tags: ["155"]
  <<: *default_rules
  stage: Verify_Template_Sync
  image: alpine:latest
  script:
    - set -u
    - apk add --no-cache git openssh
    - exit_code=0
    - git remote add upstream http://gitlab-ci-token:${CI_JOB_TOKEN}@*************:15000/tradingorchestrator/docker_template.git || exit_code=$?
    - if [ $exit_code -ne 0 ] && [ $exit_code -ne 3 ]; then exit $exit_code; fi
    - if [ $exit_code -eq 3 ]; then git remote set-url upstream http://gitlab-ci-token:${CI_JOB_TOKEN}@*************:15000/tradingorchestrator/docker_template.git; fi 
    - git fetch upstream
    - git diff --quiet upstream/dev build.py .gitlab-ci.yml README.md mypy.ini ruff.toml && exit 0
    - git diff upstream/dev build.py .gitlab-ci.yml README.md mypy.ini ruff.toml
    - |
      echo "
      some files (derived from docker_template) might be outdated. Please update them from the upstream repository and again 
      review your current changes against those new changes if applicable. Technically, it's perfectly functional to not do
      this right now so you may choose not to and skip this, but eventually you'd have to, so it's recommended to do it now. 
      on the other hand if this mismatch is because you are changing these files in this merge request, it's 
      recommended to first merge these files' changes in docker_template and then merge this."
    - exit 1
  allow_failure: true  # Allows the pipeline to continue


Linting:
  stage: Linting
  tags: ["155"]
  <<: *default_rules
  script:
    - ruff format --check --config=pipeline/ruff.toml src/
    - ruff check --config=pipeline/ruff.toml src/
  <<: *setup

Typing:
  stage: Typing
  tags: ["155"]
  <<: *default_rules
  script:
    - mypy --config-file pipeline/mypy.ini src/
  <<: *setup

Testing:
  stage: Testing
  tags: ["155"]
  <<: *default_rules
  services:
    - name: mysql:5.7
      variables:
        MYSQL_DATABASE: "test_db"
        MYSQL_ROOT_PASSWORD: "test_pwd"
  script:
    - coverage run --data-file=$CI_PROJECT_DIR/.coverage.unit.${CONDA_ENV_NAME} --rcfile=$CI_PROJECT_DIR/.coveragerc -m pytest pipeline/tests/.
    - ls $CI_PROJECT_DIR -a
  artifacts:
    paths:
      - .coverage.unit.${CONDA_ENV_NAME}
  <<: *setup

Coverage:
  tags: ["155"]
  <<: *default_rules
  stage: Coverage
  script:
    - source activate env_balte_1.0.0
    - pip install xxhash==3.5.0
    - python build.py --src . --build /opt/build
    - coverage combine
    - coverage report
    - coverage xml -o coverage.xml
    - coverage html
  coverage: '/\d+\%\s*$/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage.xml
    paths:
      - htmlcov


Publish:
  tags: ["155"]
  rules:
    - if: ($CI_PROJECT_NAME != "docker_template") && ($CI_PROJECT_NAME != "docker_base") && ($CI_PROJECT_NAME != "docker-ib")
  parallel:
    matrix:
      - BUILD_TARGET: ["test", "production"]
  stage: Publish
  script:
    - source activate env_balte_1.0.0
    - pip install xxhash==3.5.0
    - python build.py --src . --build ./build/${BUILD_TARGET}
    - cd ./build/${BUILD_TARGET}/src
    - echo ${CI_COMMIT_SHA} > .release
    - git init
    - git config user.email "<EMAIL>"
    - git config user.name "Neville Longbottom"
    - git add .
    - git commit -m "initial commit"
  artifacts:
    name: "${CI_COMMIT_REF_SLUG}:${BUILD_TARGET}"
    paths:
      - ./build/${BUILD_TARGET}/src
      - ./build/${BUILD_TARGET}/data
