# templates.yml - Configuration for template rendering
# IMPORTANT: Final paths are constructed by combining section name and path/dest
# For example, under 'src' section:
#   path: "config/mysql-master/my.cnf.template" resolves to "src/config/mysql-master/my.cnf.template"
#   dest: "config/mysql-master/my.cnf" resolves to "src/config/mysql-master/my.cnf"
#
# All paths are relative to the build directory
# Templates are rendered after the build process is complete
# Processing happens recursively for submodules, starting from the top level
# Templates are deleted after rendering, and top level template settings persist