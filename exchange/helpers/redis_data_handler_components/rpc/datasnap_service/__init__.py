from core.version_compat import IS_PY311_PLUS


if IS_PY311_PLUS:
    from exchange.helpers.redis_data_handler_components.rpc.datasnap_service_1_0_0 import (
        datasnap_service_pb2_grpc as datasnap_service_pb2_grpc,
    )
else:
    from exchange.helpers.redis_data_handler_components.rpc.datasnap_service_0_0_3 import (  # type: ignore[no-redef]
        datasnap_service_pb2_grpc as datasnap_service_pb2_grpc,
    )
