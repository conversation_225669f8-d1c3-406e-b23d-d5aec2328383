"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

import abc
import collections.abc
from toti.rpc import datasnapservice_pb2 as datasnap_service_pb2
import grpc
import grpc.aio
import typing

_T = typing.TypeVar("_T")

class _MaybeAsyncIterator(
    collections.abc.AsyncIterator[_T],
    collections.abc.Iterator[_T],
    metaclass=abc.ABCMeta,
): ...
class _ServicerContext(grpc.ServicerContext, grpc.aio.ServicerContext):  # type: ignore[misc, type-arg]
    ...

class DataSnapStub:
    def __init__(
        self, channel: typing.Union[grpc.Channel, grpc.aio.Channel]
    ) -> None: ...
    get_tick_data: grpc.UnaryUnaryMultiCallable[
        datasnap_service_pb2.DataSnapRequest,
        datasnap_service_pb2.RepeatedArrayReply,
    ]

    get_latest_second: grpc.UnaryUnaryMultiCallable[
        datasnap_service_pb2.TimeStampRequest,
        datasnap_service_pb2.TimestampResponse,
    ]

class DataSnapAsyncStub:
    get_tick_data: grpc.aio.UnaryUnaryMultiCallable[
        datasnap_service_pb2.DataSnapRequest,
        datasnap_service_pb2.RepeatedArrayReply,
    ]

    get_latest_second: grpc.aio.UnaryUnaryMultiCallable[
        datasnap_service_pb2.TimeStampRequest,
        datasnap_service_pb2.TimestampResponse,
    ]

class DataSnapServicer(metaclass=abc.ABCMeta):
    @abc.abstractmethod
    def get_tick_data(
        self,
        request: datasnap_service_pb2.DataSnapRequest,
        context: _ServicerContext,
    ) -> typing.Union[
        datasnap_service_pb2.RepeatedArrayReply,
        collections.abc.Awaitable[datasnap_service_pb2.RepeatedArrayReply],
    ]: ...
    @abc.abstractmethod
    def get_latest_second(
        self,
        request: datasnap_service_pb2.TimeStampRequest,
        context: _ServicerContext,
    ) -> typing.Union[
        datasnap_service_pb2.TimestampResponse,
        collections.abc.Awaitable[datasnap_service_pb2.TimestampResponse],
    ]: ...

def add_DataSnapServicer_to_server(
    servicer: DataSnapServicer, server: typing.Union[grpc.Server, grpc.aio.Server]
) -> None: ...
