import logging
from typing import Dict, List, Optional
from core.helpers.one_second_exit_application_components.data_client import (
    OneSecondExitApplicationDataClientBase,
)
from redis import asyncio as aioredis
from exchange.helpers.configstore import REDIS_HOST, REDIS_PORT


class OneSecondExitApplicationDataClient(OneSecondExitApplicationDataClientBase):
    """
    Handles market data retrieval for the One Second Exit Application.

    This class serves as an interface for fetching the latest traded prices of active
    positions using `balte_id`s. It is intended to be implemented with a concrete
    data-fetching mechanism, such as querying an external API, database, or market
    data feed.

    Attributes:
        one_second_exit_application_logger (logging.Logger):
            Logger instance for logging data retrieval activities.
        redis_client (aioredis.Redis):
            Redis client instance for reading data in pipelined manner.
    """

    def __init__(self, logger: logging.Logger) -> None:
        """
        Initializes an instance of OneSecondExitApplicationDataClient class.

        Args:
            logger (logging.Logger): Logger for tracking data-related operations.
        """
        super().__init__(logger=logger)
        self.redis_client: Optional[aioredis.Redis] = None

    async def connect_to_redis(self) -> None:
        """Establishes a connection to the Redis server if not already connected."""
        if self.redis_client is None:
            self.redis_client = await aioredis.from_url(  # type: ignore
                f"redis://{REDIS_HOST}:{REDIS_PORT}", decode_responses=True
            )
            self.one_second_exit_application_logger.info("Connected to Redis server.")

    async def disconnect_from_redis(self) -> None:
        """Closes the Redis connection if it is active."""
        if self.redis_client:
            await self.redis_client.close()  # type: ignore
            self.redis_client = None
        self.one_second_exit_application_logger.info("Disconnected from Redis server.")

    async def pipelined_read_from_redis(self, keys: List[int]) -> Dict[int, float]:
        """
        Fetches multiple keys from Redis using a pipeline for efficiency.

        Args:
            keys (List[int]): List of Redis keys to fetch values for.

        Returns:
            Dict[int, float]: A dictionary mapping keys (balte_id) to their values (ltp).
        """
        if not self.redis_client:
            raise ConnectionError(
                "Redis client is not connected. Call 'connect' first."
            )

        async with self.redis_client.pipeline() as pipe:
            for key in keys:
                await pipe.get(str(key))
            results = await pipe.execute()

        return {
            key: float(result)
            for key, result in zip(keys, results)
            if result is not None
        }

    async def fetch_data(self, active_balte_ids: List[int]) -> Dict[int, float]:
        """
        Fetches the latest traded price data for the provided `balte_id`s.

        Args:
            active_balte_ids (List[int]): List of `balte_id`s representing active positions.

        Returns:
            Dict[int, float]: A dictionary mapping `balte_id`s to their latest traded prices.
        """
        try:
            await self.connect_to_redis()
            last_traded_price_info: Dict[
                int, float
            ] = await self.pipelined_read_from_redis(keys=active_balte_ids)
            return last_traded_price_info
        except Exception as e:
            self.one_second_exit_application_logger.error(
                f"Error fetching data from redis - {repr(e)}"
            )
            await self.disconnect_from_redis()
            return {}
