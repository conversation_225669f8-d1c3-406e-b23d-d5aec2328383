from core.dags.monitoring.restrictions_alert_builder_base import (
    RestrictionListAlertDagBuilderBase,
)
from airflow.models import DAG


class RestrictionListAlertDagBuilder(RestrictionListAlertDagBuilderBase):
    """
    Each exchange can override this file to add exchange specific behaviour
    to each dag
    """

    pass


restrictions_alert_dag_builder = RestrictionListAlertDagBuilder(
    dag_id="send_restricitons_alert", schedule_interval="57 23 * * *"
)
dag = restrictions_alert_dag_builder.build_dag()
