from core.dags.ops.oms_ops_builder_base import (
    OMSOneSecondExitApplicationDagBuilderBase,
)
from airflow.models import DAG


class OMSOneSecondExitApplicationDagBuilder(OMSOneSecondExitApplicationDagBuilderBase):
    """
    Each exchange can override this file to add exchange specific behaviour
    to each dag
    """

    pass


one_second_exit_application_builder = OMSOneSecondExitApplicationDagBuilder(
    dag_id="OMS_ONE_SECOND_EXIT_APPLICATION", schedule_interval="45 08 * * 1-5"
)
dag = one_second_exit_application_builder.build_dag()
