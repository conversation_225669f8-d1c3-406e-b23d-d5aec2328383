import asyncio
import os
import logging
from typing import Dict, Optional, Sequence
import pandas as pd
from redis import asyncio as aioredis
from core.helpers.alerting import send_message_to_gspace
from exchange.helpers.configstore import REDIS_HOST, REDIS_PORT
from core.helpers.configstore import <PERSON><PERSON>CH<PERSON><PERSON>_TYPE, WEBHOOK
from core.helpers.utils import get_local_timestamp
from exchange.helpers.redis_data_handler_components.datasnap_client import (
    DataSnapClient,
)
import balte.balte_config
from balte.utility_inbuilt import balte_initializer
from toti.toti_utility import parse_contract, contract_name_to_balte_id


class RedisDataHandlerRunnerBase:
    """
    A Redis client for handling real-time tick data storage and retrieval.

    This class provides functionality to connect to Redis, store tick data
    with balte_id to LTP (Last Traded Price) mappings, and continuously
    fetch and store market data.

    Attributes:
        redis_host (str): Redis server hostname from configuration
        redis_port (int): Redis server port from configuration
        redis_pool (aioredis.Redis): Redis connection pool for async operations
        datasnap_client (DataSnapClient): Client for fetching data from datasnap service
        is_market_closed (asyncio.Future): Flag to indicate if market is closed
        market_closing_time (pd.Timestamp): Timestamp of market closing time
        redis_data_handler_logger (logging.Logger): Logger instance for recording application activity
        contract_to_balte_id_cache (Dict[str, int]): Cache to store contract name to balte_id mappings
    """

    def __init__(self) -> None:
        """
        Initialize the Redis data client.

        Sets up Redis connection parameters from configuration and
        initializes the Redis connection pool.
        """
        self.redis_host: str = REDIS_HOST
        self.redis_port: int = int(REDIS_PORT)
        self.redis_db: str = ""
        self.redis_client: Optional[aioredis.Redis] = None
        self.redis_data_handler_logger: logging.Logger = self.setup_logger()
        self.datasnap_client: DataSnapClient = DataSnapClient(
            logger=self.redis_data_handler_logger
        )
        self.is_market_closed: asyncio.Future = asyncio.Future()  # type: ignore
        # Initialize BaLTE config
        balte_initializer(EXCHANGE_TYPE.lower())
        self.market_closing_time: pd.Timestamp = (
            get_local_timestamp()
            .normalize()
            .replace(
                hour=balte.balte_config.MARKET_CLOSE_HOUR,
                minute=balte.balte_config.MARKET_CLOSE_MINUTE,
            )
        )
        # Cache to store contract name to balte_id mappings
        self.contract_to_balte_id_cache: Dict[str, int] = {}
        self.redis_data_handler_logger.info("Redis Data Handler is LIVE")

    def setup_logger(self) -> logging.Logger:
        """
        Sets up and configures the RedisDataHandlerRunnerBase logger.

        It sets the logging level to INFO and adds a file handler to
        store logs in the `/opt/balte_live/log/redis_data_handler` directory.

        Returns:
            logging.Logger: Configured logger instance for the application.
        """
        LOG_DIR = "/opt/balte_live/log/redis_data_handler"
        LOG_FILE = os.path.join(
            LOG_DIR,
            f"redis_data_handler.log.{get_local_timestamp().normalize().strftime('%Y-%m-%d')}",
        )
        logger: logging.Logger = logging.getLogger("redis_data_handler")
        logger.setLevel(logging.INFO)
        fh = logging.FileHandler(LOG_FILE)
        fh.setLevel(logging.INFO)
        formatter = logging.Formatter(
            "%(asctime)s.%(msecs)03d %(levelname)s %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
        )
        fh.setFormatter(formatter)
        logger.addHandler(fh)
        return logger

    async def connect_to_redis(self) -> None:
        """Establishes a connection to the Redis server if not already connected."""
        if self.redis_client is None:
            self.redis_data_handler_logger.info(
                f"Connecting to Redis at {REDIS_HOST}:{REDIS_PORT}"
            )
            self.redis_client = await aioredis.from_url(  # type: ignore
                f"redis://{REDIS_HOST}:{REDIS_PORT}", decode_responses=True
            )
            self.redis_data_handler_logger.info("Successfully connected to Redis")

    def get_balte_id(self, contract: str) -> int:
        """
        Get the balte_id for a given contract.

        First checks the cache for existing mapping, if not found then calculates
        and stores in cache for future use.

        Args:
            contract (str): The contract symbol

        Returns:
            int: The balte_id associated with the contract
        """
        if contract in self.contract_to_balte_id_cache:
            return self.contract_to_balte_id_cache[contract]

        _, expiry, option_type, _ = parse_contract(contract)

        balte_id: int = -1
        try:
            if option_type != "":
                balte_id = contract_name_to_balte_id(
                    universe="optcom", contract_name=contract
                )
            elif expiry != "":
                balte_id = contract_name_to_balte_id(
                    universe="mcx_fut_near", contract_name=contract
                )
            else:
                balte_id = contract_name_to_balte_id(
                    universe="mcx_spot", contract_name=contract
                )
        except Exception as e:
            if "Unknown underlying symbol in contract" not in repr(e):
                self.redis_data_handler_logger.error(
                    f"Error getting balte_id for contract {contract}: {repr(e)}"
                )

        self.contract_to_balte_id_cache[contract] = balte_id
        return balte_id

    def clear_balte_id_cache(self) -> None:
        """
        Clear the contract to balte_id cache.

        This method can be used to clear the cache if needed.
        """
        self.contract_to_balte_id_cache.clear()

    async def store_balte_id_to_ltp_in_redis(self, tick_data: Sequence[str]) -> None:
        """
        Store balte_id to Last Traded Price (LTP) mappings in Redis.

        Processes tick data and stores each contract's LTP in Redis using
        the balte_id as the key. Operations are performed concurrently
        for better performance.

        Args:
            tick_data (list): List of tick data strings in format "id|field1|ltp|..."
                            where contract is second element and LTP is fourth element

        Raises:
            Exception: If Redis pool is not initialized

        Note:
            Assumes tick data format: "contract|field1|ltp|other_fields"
            All operations are executed concurrently using asyncio.gather()
        """
        if self.redis_client is None:
            self.redis_data_handler_logger.error("Redis client is not initialized")
            raise Exception("Redis client is not initialized")

        async with self.redis_client.pipeline(transaction=False) as pipe:
            for tick in tick_data:
                tick = str(tick)
                contract = tick.split("|")[1]
                balte_id = self.get_balte_id(contract=contract)

                if balte_id == -1:
                    continue

                ltp = tick.split("|")[3]

                await pipe.set(str(balte_id), ltp)
            await pipe.execute()

    async def market_termination_checker(self) -> None:
        """Calculates the time remaining in market closing and sleeps for that time.

        This function calculates the time remaining in market closing and sleeps for that time.
        Post that, it sets a flag which signals other async processes to stop as well.
        """
        seconds_until_market_termination: float = max(
            0, (self.market_closing_time - get_local_timestamp()).total_seconds()
        )
        self.redis_data_handler_logger.info(
            f"Market will close in {seconds_until_market_termination} seconds"
        )
        await asyncio.sleep(seconds_until_market_termination)
        self.redis_data_handler_logger.info(
            "Market closed, stopping Redis Data Handler."
        )
        self.is_market_closed.set_result(True)

    async def fetch_and_store_data(self) -> None:
        """
        Continuously fetch market data and store it in Redis.

        Runs an infinite loop that:
        1. Gets the latest timestamp from the data source
        2. Fetches tick data for that timestamp
        3. Stores the tick data in Redis as ID-LTP mappings

        Args:
            get_latest_second (callable): Function that returns an object with
                                        a 'timestamp' attribute containing the latest second
            get_tick_data (callable): Function that takes a timestamp and returns
                                    an object with a 'message' attribute containing tick data

        Note:
            This method runs indefinitely until an exception occurs.
            Errors are caught and printed but the loop continues.
        """
        while not self.is_market_closed.done():
            try:
                await self.connect_to_redis()
                latest_timestamp = (
                    self.datasnap_client.get_latest_second(segment="MCX")
                ).timestamp
                tick_data = self.datasnap_client.get_tick_data(
                    segment="MCX", timestamp=latest_timestamp
                )
                await self.store_balte_id_to_ltp_in_redis(tick_data.message)
            except Exception as e:
                msg = f"Redis Data Handler failing\n\n```{repr(e)}```"
                send_message_to_gspace(msg=msg, url=WEBHOOK)
                self.redis_data_handler_logger.error(
                    f"Error occurred while fetching and storing data: {repr(e)}"
                )

        # Clear cache and close datasnap client after market close
        self.clear_balte_id_cache()
        self.datasnap_client.close()

    async def redis_data_client_runner(self) -> None:
        """
        Run the core event loop for the Redis data client.

        Starts the continuous data fetching and storage process by running
        the fetch_and_store_data method in the asyncio event loop.

        Note:
            This method blocks until the fetch_and_store_data loop is interrupted.
            Uses the current event loop to run the async operations.
        """
        await asyncio.gather(
            self.fetch_and_store_data(), self.market_termination_checker()
        )

    def run(self) -> None:
        """
        Starts the Redis data client.

        This method initializes the asyncio event loop and runs the
        redis_data_client_runner coroutine within it. It blocks until
        the event loop is stopped.
        """
        asyncio.run(self.redis_data_client_runner())
