from core.runners.live_trade_pickle_db_base import LiveTradePickleDbRunnerBase
from core.helpers.configstore import SEGMENT


class LiveTradePickleDbRunner(LiveTradePickleDbRunnerBase):
    """
    Each exchange can override this file to add exchange specific behaviour
    to each runner
    """

    pass


if __name__ == "__main__":
    live_trade_pickle_db_runner = LiveTradePickleDbRunner(
        allowed_segments=SEGMENT.split(","),
        allowed_universes=["optcom", "optcom_onemin"],
    )
    live_trade_pickle_db_runner.run()
