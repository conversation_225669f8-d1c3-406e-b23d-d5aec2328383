from core.runners.watchdog_runner_base import WatchdogRunnerBase
from toti import Toti
from core.helpers.configstore import (
    EXCHANGE_TYPE,
    KAFKA_HOST,
    KAFKA_PORT,
    OMS_HOST,
    OMS_PORT,
)
import balte


class WatchdogRunner(WatchdogRunnerBase):
    def __init__(self) -> None:
        balte.utility_inbuilt.balte_initializer(EXCHANGE_TYPE.lower())
        balte.balte_config.KAFKA_IP = f"{KAFKA_HOST}:{KAFKA_PORT}"
        Toti.set_production_mode(f"{OMS_HOST}:{OMS_PORT}")


if __name__ == "__main__":
    watchdog_runner = WatchdogRunner()
    watchdog_runner.run()
