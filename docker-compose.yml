services: 
  redis:
    image: ${CONTAINER_REGISTRY_BASE}/redis:latest
    hostname: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}_redis
    container_name: ${EXCHANGE_TYPE}_${EXECUTION_LEVEL}_redis
    restart: always
    ports:
      - "${REDIS_EXPOSED_PORT}:${REDIS_PORT}"
    networks:
      - custom_network
    command: ["redis-server"]
    env_file:
      - .env
    volumes:
      - ./localtime:/etc/localtime:ro
    healthcheck:
      test: ["CMD", "redis-cli", "-p", "${REDIS_PORT}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
